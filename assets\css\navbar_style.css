/* تعریف فونت سمیم */
@font-face {
    font-family: 'Samim';
    src: url('assets/fonts/Samim.eot');
    src: url('assets/fonts/Samim.eot?#iefix') format('embedded-opentype'),
        url('assets/fonts/Samim.woff2') format('woff2'),
        url('assets/fonts/Samim.woff') format('woff'),
        url('assets/fonts/Samim.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* استایل کلی برای بدنه صفحه */
html,
body {
    margin: 0;
    padding: 0;
    min-height: 100%;
    position: relative;
    font-family: '<PERSON><PERSON>', tahoma, Arial, sans-serif;
}

/* استایل برای body در حالت موبایل با نوار ناوبری پایین */
body.has-mobile-nav {
    padding-bottom: 60px !important;
}

/* استایل کلی ناوبار */
.navbar {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 5px 20px;
    height: 96px;
}

/* استایل منوی موبایل */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    height: 100vh;
    background: white;
    padding: 20px;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 2000;
    overflow-y: auto !important;
    /* اسکرول عمودی فعال */
    overflow-x: hidden !important;
    /* اسکرول افقی غیرفعال */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding-top: 60px;
    display: flex;
    flex-direction: column;
}

.mobile-menu.active {
    right: 0;
}

/* استایل برای هدر بخش‌های منو */
.menu-section-header {
    padding: 0.25rem 15px;
    color: #3b82f6;
    font-weight: 500;
    text-align: right;
    font-family: 'Samim', tahoma, Arial, sans-serif;
    margin: 0;
    /* حذف تمام margin‌ها */
}

.account-section-header {
    padding: 0.25rem 15px;
    color: #3b82f6;
    font-weight: 500;
    text-align: right;
    font-family: 'Samim', tahoma, Arial, sans-serif;
    margin: 0;
    /* حذف تمام margin‌ها */
}

/* اضافه کردن استایل جدید برای فاصله‌گذاری دقیق */
.mobile-menu>* {
    margin-bottom: 0 !important;
}

.mobile-menu>.menu-section-header {
    margin-top: 3px !important;
}

.mobile-menu>.account-section-header {
    margin-top: 1px !important;
}

/* استایل برای منوی موبایل */
.mobile-menu .menu {
    list-style: none;
    padding: 0;
    margin: 0;
    /* حذف تمام margin‌ها */
    width: 100%;
    display: flex;
    flex-direction: column;
}

.mobile-menu .menu li {
    width: 100%;
    margin-bottom: 2px;
    /* کاهش فاصله بین آیتم‌ها */
    flex: 0 0 auto;
    /* جلوگیری از تغییر اندازه خودکار */
}

.mobile-menu .menu li a {
    display: block;
    width: 100%;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
    text-align: right;
    transition: all 0.3s ease;
}

.mobile-menu .menu li a:hover,
.mobile-menu .menu li a.active {
    background-color: #f5f5f5;
    color: #007bff;
}

/* خط جداکننده بین بخش‌های منو */
.menu-divider {
    width: 100%;
    height: 1px;
    background-color: #e0e0e0;
    margin: 0 !important;
    /* حذف کامل margin با !important */
    padding: 0 !important;
}

/* حذف فاصله بین آخرین آیتم منوی اصلی و خط جداکننده */
.mobile-menu>ul.menu:first-of-type {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* حذف فاصله بین خط جداکننده و هدر حساب کاربری */
.mobile-menu>.menu-divider+.account-section-header {
    margin-top: 0 !important;
    padding-top: 0.1rem !important;
}

/* تنظیم دقیق فاصله‌ها برای همه عناصر منو */
.mobile-menu>* {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* اطمینان از اعمال استایل‌ها با اولویت بالا */
.mobile-menu .menu li:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* کاهش فاصله بین هدرها و منوها */
.menu-section-header+.menu,
.account-section-header+.menu {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* کاهش فاصله بین هدر حساب کاربری و خط جداکننده */
.mobile-menu>.account-section-header {
    margin-top: 0 !important;
    /* حذف فاصله بالایی */
    padding-top: 0.1rem;
    /* کاهش padding بالایی */
}

/* کاهش فاصله بین آیتم‌های منو */
.mobile-menu .menu li {
    margin-bottom: 2px;
    /* کاهش فاصله بین آیتم‌ها */
}

/* تنظیم فاصله بین منوی اصلی و خط جداکننده */
.mobile-menu>.menu:first-of-type {
    margin-bottom: 2px;
}

/* استایل برای اسکرول */
.mobile-menu::-webkit-scrollbar {
    width: 6px;
}

.mobile-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.mobile-menu::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.mobile-menu::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* استایل دکمه همبرگر */
.hamburger-menu {
    display: none;
    cursor: pointer;
    z-index: 2500;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    width: 40px;
    text-align: center;
}

.hamburger-menu .bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    background-color: #333;
    transition: all 0.3s ease-in-out;
}

/* انیمیشن برای دکمه همبرگر */
.hamburger-menu.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
}

.hamburger-menu.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger-menu.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
}

/* استایل برای overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    display: none;
}

.overlay.active {
    display: block;
}

/* تنظیمات برای حالت موبایل */
@media (max-width: 768px) {
    .hamburger-menu {
        display: block;
        width: 45px;
        text-align: center;
        position: absolute;
        right: 10px;
        top: 65%;
        transform: translateY(-50%);
    }

    .menu.menu-horizontal {
        display: none;
    }

    body.menu-open {
        overflow: hidden;
    }

    /* مخفی کردن دکمه برگشت به بالا در حالت موبایل */
    .scroll-to-top {
        display: none !important;
    }
}

/* انیمیشن برای تمام آیتم‌های منو */
.mobile-menu .menu li,
.mobile-menu .account-section-header,
.mobile-menu .menu-section-header {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.mobile-menu.active .menu li,
.mobile-menu.active .account-section-header,
.mobile-menu.active .menu-section-header {
    opacity: 1;
    transform: translateX(0);
}

/* تأخیر انیمیشن برای آیتم‌های منو */
.mobile-menu .menu li:nth-child(1) {
    transition-delay: 0.2s;
}

.mobile-menu .menu li:nth-child(2) {
    transition-delay: 0.3s;
}

.mobile-menu .menu li:nth-child(3) {
    transition-delay: 0.4s;
}

.mobile-menu .menu li:nth-child(4) {
    transition-delay: 0.5s;
}

.mobile-menu .menu li:nth-child(5) {
    transition-delay: 0.6s;
}

.mobile-menu .menu li:nth-child(6) {
    transition-delay: 0.7s;
}

.mobile-menu .menu li:nth-child(7) {
    transition-delay: 0.8s;
}

.mobile-menu .menu li:nth-child(8) {
    transition-delay: 0.9s;
}

.mobile-menu .menu li:nth-child(9) {
    transition-delay: 1.0s;
}

.mobile-menu .menu li:nth-child(10) {
    transition-delay: 1.1s;
}

.mobile-menu .menu li:nth-child(11) {
    transition-delay: 1.2s;
}

.mobile-menu .menu li:nth-child(12) {
    transition-delay: 1.3s;
}

.mobile-menu .menu li:nth-child(13) {
    transition-delay: 1.4s;
}

.mobile-menu .menu li:nth-child(14) {
    transition-delay: 1.5s;
}

.mobile-menu .menu li:nth-child(15) {
    transition-delay: 1.6s;
}

.mobile-menu .menu li:nth-child(16) {
    transition-delay: 1.7s;
}

.mobile-menu .menu li:nth-child(17) {
    transition-delay: 1.8s;
}

.mobile-menu .menu li:nth-child(18) {
    transition-delay: 1.9s;
}

.mobile-menu .menu li:nth-child(19) {
    transition-delay: 2.0s;
}

.mobile-menu .menu li:nth-child(20) {
    transition-delay: 2.1s;
}

/* تأخیر انیمیشن برای هدرهای بخش */
.mobile-menu .account-section-header {
    transition-delay: 0.1s;
}

.mobile-menu .menu-section-header {
    transition-delay: 0.7s;
}

/* اضافه کردن کلاس‌های جدید برای تمایز منوها */
.mobile-menu .account-menu {
    margin-top: 0 !important;
}

.mobile-menu .main-menu {
    margin-top: 0 !important;
}

/* تنظیم فاصله بین منوها و خط جداکننده */
.mobile-menu>.account-menu {
    margin-bottom: 0 !important;
}

.mobile-menu>.menu-divider {
    margin: 0 !important;
}

.mobile-menu>.menu-divider+.menu-section-header {
    margin-top: 0 !important;
    padding-top: 0.1rem !important;
}

.mobile-menu-header {
    position: fixed;
    top: 0;
    right: 0;
    width: 80%;
    background: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
    z-index: 2001;
}

.mobile-menu-header h3 {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    padding-right: 10px;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.close-menu {
    position: absolute;
    top: 20px;
    left: 20px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #333;
    cursor: pointer;
    padding: 5px;
}

.close-menu:hover {
    color: #3b82f6;
}

.mobile-menu .menu {
    margin-top: 0.5rem;
    padding-bottom: 0.5rem;
    flex: 1;
    overflow-y: auto;
}

.mobile-menu .menu li {
    margin-bottom: 0.25rem;
}

.mobile-menu .menu li:last-child {
    margin-bottom: 0;
}

/* استایل برای اسکرول */
.mobile-menu::-webkit-scrollbar {
    width: 6px;
}

.mobile-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.mobile-menu::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.mobile-menu::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.mobile-menu .menu li a {
    display: block;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
    text-align: right;
    transition: all 0.3s ease;
}

.mobile-menu .menu li a:hover {
    background-color: #f5f5f5;
    transform: translateX(-5px);
}

.mobile-menu .menu li:last-child a {
    color: #333;
    border: none;
    margin-top: 0;
    padding-top: 12px;
}

.menu-divider {
    margin: 0.25rem 0;
    border-top: none;
}

.account-section-header {
    padding: 0.25rem 15px;
    color: #3b82f6;
    font-weight: 500;
    text-align: right;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.menu-section-header {
    padding: 0.25rem 15px;
    color: #3b82f6;
    font-weight: 500;
    text-align: right;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.hamburger-menu {
    display: none;
    cursor: pointer;
    width: 60px;
    height: 40px;
    position: relative;
    z-index: 2001;
    font-size: 24px;
}

.hamburger-menu:hover {
    background: transparent;
}

.hamburger-menu .bar {
    width: 24px;
    height: 3px;
    background: #333;
    margin: 4px auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: block;
    border-radius: 2px;
    transform-origin: center;
}

.hamburger-menu.active .bar:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
}

.hamburger-menu.active .bar:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.hamburger-menu.active .bar:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.overlay.active {
    display: block;
    opacity: 1;
}

/* پروفایل و سبد خرید */
.user-section {
    display: flex;
    align-items: center;
    gap: 25px;
    height: 100%;
}

.user-section .profile-button img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

/* استایل منوها */
.menu li a {
    font-weight: bold;
    color: black;
    background: transparent !important;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
    font-family: 'Samim', tahoma, Arial, sans-serif;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

.menu li a:hover,
.menu li a:focus,
.menu li a:active {
    color: #3b82f6;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

.menu li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #3b82f6;
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.menu li a:hover::after {
    transform: scaleX(1);
}

/* استایل حالت active برای منوهای اصلی */
.menu.menu-horizontal li a.active {
    color: #3b82f6 !important;
    background: transparent !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.menu.menu-horizontal li a.active::after {
    transform: scaleX(1);
}

/* استایل حالت active برای منوهای موبایل */
.mobile-menu .menu li a.active {
    color: #3b82f6 !important;
    background: transparent !important;
    border-right: 4px solid #3b82f6;
}

.menu li a.relative span.absolute {
    background-color: #3b82f6;
}

/* تنظیم فضای بیشتر بین آیتم‌های منو */
.menu.menu-horizontal {
    gap: 1.5rem;
    background: transparent !important;
}

/* منوی کشویی */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: -90px;
    background: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    list-style: none;
    padding: 10px 0;
    width: 140px;
    display: none;
    z-index: 1500;
    margin-top: 5px;
    opacity: 0;
    transform-style: preserve-3d;
    perspective: 1000px;
    transform: rotateX(-15deg);
    transform-origin: top center;
    transition: all 0.4s cubic-bezier(0.17, 0.67, 0.21, 1.69);
    overflow: hidden;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, #3b82f6, #1e40af);
    transform: translateY(-100%);
    transition: transform 0.3s 0.1s ease;
}

.dropdown-menu.active {
    display: block;
    opacity: 1;
    transform: rotateX(0deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.dropdown-menu.active::before {
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
    opacity: 0;
    transform: translateY(10px);
    text-align: right;
    background: transparent !important;
    position: relative;
    overflow: hidden;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.dropdown-menu a::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 136, 204, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.dropdown-menu a:hover {
    background: linear-gradient(to right, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05)) !important;
    color: #3b82f6;
    transform: translateX(-5px);
}

.dropdown-menu a:hover::before {
    transform: translateX(0);
}

.dropdown-menu.active a {
    animation: fadeInUp 0.5s forwards;
}

.dropdown-menu.active a:nth-child(1) {
    animation-delay: 0.1s;
}

.dropdown-menu.active a:nth-child(2) {
    animation-delay: 0.2s;
}

.dropdown-menu.active a:nth-child(3) {
    animation-delay: 0.3s;
}

.dropdown-menu.active a:nth-child(4) {
    animation-delay: 0.4s;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* استایل دکمه سبد خرید */
.cart-button {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
    color: black;
    text-decoration: none;
    padding: 8px 15px;
    border: none;
    transition: all 0.3s ease;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.cart-button i {
    font-size: 24px;
}

.cart-button span {
    font-size: 16px;
    font-weight: bold;
}

.cart-button:hover {
    color: #00a86b;
    /* تغییر رنگ به سبز در حالت hover */
}

.logo-container {
    width: 146px;
    height: 146px;
    flex-shrink: 0;
}

.logo-container img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* استایل باکس جستجو */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #000000;
    border-radius: 20px;
    /* گوشه‌های گرد */
    padding: 6px 12px;
    /* پدینگ کمتر برای نازک‌تر شدن */
    margin-left: 10px;
    width: 300px;
    /* عرض بیشتر برای طولانی‌تر بودن */
    transition: all 0.3s ease;
    border: 1px solid transparent;
    /* حاشیه شفاف در حالت عادی */
}

.search-box:focus-within {
    border-color: #0088ff;
    /* کادر آبی هنگام فوکوس */
    border-width: 2.3px;
    /* افزایش ضخامت حاشیه هنگام فوکوس */
    box-shadow: 0 0 0 3px rgba(0, 136, 255, 0.2);
    /* سایه محو آبی بزرگتر */
}

.search-icon {
    color: #0088ff;
    /* رنگ آبی برای آیکون ذره‌بین */
    margin-left: 8px;
}

.search-input {
    background-color: transparent;
    border: none;
    color: #ffffff;
    width: 100%;
    /* استفاده از کل فضای موجود */
    padding: 4px;
    outline: none;
}

.search-input::placeholder {
    color: #aaaaaa;
}

/* استایل نتایج جستجو */
.search-results {
    position: absolute;
    top: 45px;
    right: 0;
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
    padding: 10px;
}

.search-section {
    margin-bottom: 15px;
}

.search-section h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.search-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.2s;
    text-decoration: none;
    color: #333;
}

.search-item:hover {
    background: #f5f7fa;
}

.search-result-image,
.search-result-no-image {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-left: 10px;
    object-fit: cover;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #aaa;
}

.search-item-details {
    display: flex;
    flex-direction: column;
}

.search-item-name {
    font-size: 13px;
    font-weight: 500;
}

.search-item-price {
    font-size: 12px;
    color: #3498db;
    margin-top: 3px;
}

.view-all-results {
    text-align: center;
    padding: 10px;
    border-top: 1px solid #eee;
    margin-top: 5px;
}

.view-all-results a {
    color: #3498db;
    font-size: 13px;
    text-decoration: none;
}

.view-all-results a:hover {
    text-decoration: underline;
}

.no-results {
    padding: 15px;
    text-align: center;
    color: #666;
    font-size: 14px;
}

/* انیمیشن لودینگ */
.search-loading {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.search-loading .dot {
    width: 8px;
    height: 8px;
    background: #3498db;
    border-radius: 50%;
    margin: 0 3px;
    animation: bounce 1.5s infinite ease-in-out;
}

.search-loading .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.search-loading .dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-10px);
    }
}

/* استایل دکمه ورود/ثبت‌نام */
.login-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: black;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 5px;
    transition: all 0.3s ease;
    flex-direction: row-reverse;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.login-button:hover {
    color: #0088cc;
}

.login-button:hover .text-container span {
    color: #0088cc;
}

.login-button .text-container span {
    display: inline;
    transition: color 0.3s ease;
}

/* اضافه کردن Overlay برای منوهای موبایل */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1999;
    display: none;
}

.overlay.active {
    display: block;
}

/* استایل نوار سیاه بالای هدر */
.top-black-bar {
    background-color: #212121;
    /* رنگ خاکستری تیره به جای مشکی */
    color: white;
    display: flex;
    justify-content: space-between;
    padding: 8px 20px;
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .top-black-bar {
        position: static; /* Changed to static to scroll with page */
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px 0;
        font-size: 12px;
    }

    .top-black-bar .hide-on-mobile {
        display: none !important;
        /* Added !important to ensure it hides */
    }

    .top-black-bar .top-bar-center {
        position: static;
        transform: none;
        width: auto;
        text-align: center;
    }
}

.top-black-bar .top-bar-right {
    margin-right: 20px;
    width: 33%;
    text-align: right;
}

.top-bar-right span:first-child {
    color: #3b82f6;
    font-weight: bold;
}

.top-black-bar .top-bar-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    text-align: center;
    z-index: 5;
}

.top-black-bar .top-bar-left {
    margin-left: 20px;
    display: flex;
    align-items: center;
    width: 33%;
    text-align: left;
    justify-content: flex-end;
}

.top-black-bar .top-bar-left i {
    font-size: 14px;
    margin-right: 5px;
    color: #3b82f6;
    /* تغییر رنگ به آبی */
}

.top-black-bar span {
    margin-left: 0px;
}

.top-black-bar .discount-code {
    color: #3b82f6;
    font-weight: 700;
    margin: 0 2px;
}

/* This block has been merged with the rule above to avoid conflicts. */

/* استایل نوار ناوبری موبایل */
.mobile-bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    height: 70px;
    align-items: center;
    justify-content: space-around;
    z-index: 2001;
    /* افزایش z-index برای قرار گرفتن روی منوی موبایل */
    padding: 0 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    border-top: 1px solid #eaeaea;
}

.mobile-bottom-nav .button-container {
    display: flex;
    width: 100%;
    justify-content: space-around;
    align-items: center;
    height: 100%;
}

.mobile-bottom-nav .button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #333;
    text-decoration: none;
    font-size: 12px;
    gap: 5px;
    position: relative;
    width: 33.33%;
    height: 100%;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.mobile-bottom-nav .button:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 15px;
    bottom: 15px;
    width: 1px;
    background-color: #eaeaea;
}

.mobile-bottom-nav .button .icon {
    font-size: 24px;
}

/* باکس جستجوی موبایل */
.mobile-search-box {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 2001;
    /* افزایش z-index */
    padding: 20px;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.mobile-search-box.active {
    display: block;
}

.mobile-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.mobile-search-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    margin-bottom: 20px;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.mobile-search-results {
    max-height: calc(100vh - 150px);
    overflow-y: auto;
    font-family: 'Samim', tahoma, Arial, sans-serif;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .mobile-bottom-nav {
        display: flex !important;
        width: calc(100% - 24px) !important;
        left: 12px !important;
        right: auto !important;
        bottom: 12px !important;
        border-radius: 25px !important;
        height: 65px !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
        border-top: none !important;
        background-color: white !important;
    }

    .search-box,
    .cart-button {
        display: none;
    }

    .user-section {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .profile-button {
        display: block;
    }
}

/* اصلاح خط جداکننده برای دکمه جستجو */
#mobileSearchButton::after {
    content: '';
    position: absolute;
    right: 0;
    top: 15px;
    bottom: 15px;
    width: 1px;
    background-color: #eaeaea;
}

/* استایل‌های بخش حساب کاربری در منوی موبایل */
.account-section {
    margin: 1rem 0;
    padding: 0.5rem 0;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #4b5563;
    text-decoration: none;
    transition: all 0.3s ease;
    font-family: 'Samim', tahoma, Arial, sans-serif;
}

.mobile-menu-item:hover {
    background: #f8fafc;
    color: #3b82f6;
}

.mobile-menu-item.active {
    color: #3b82f6;
    background: #eff6ff;
    border-right: 4px solid #3b82f6;
}

.mobile-menu-item i {
    width: 1.25rem;
    text-align: center;
}

.mobile-menu-item.logout-item {
    color: #ef4444;
}

.mobile-menu-item.logout-item:hover {
    background: #fef2f2;
    color: #dc2626;
}

.account-section-divider {
    margin: 1rem 0;
    padding: 0.5rem 0;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
}

/* حذف منوی کشویی در حالت موبایل */
@media (max-width: 768px) {
    .profile-dropdown {
        display: none;
    }

    .profile-button {
        display: block;
        width: 40px;
        height: 40px;
    }

    .profile-button img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }
}

/* Media Queries */
@media (max-width: 768px) {
    .hamburger-menu {
        display: block;
        width: 45px;
        text-align: center;
        position: absolute;
        right: 10px;
        top: 65%;
        transform: translateY(-50%);
    }

    .menu.menu-horizontal {
        display: none;
    }

    .logo-container {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 146px;
        height: 146px;
        z-index: 10;
    }

    .logo-container img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .navbar {
        padding: 5px 15px !important;
        position: relative !important; /* Removed stickiness */
        margin: 10px auto 0; /* Center the navbar with top margin */
        width: calc(100% - 24px) !important; /* Explicitly set width */
        border-radius: 25px !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
        z-index: 1001 !important;
        height: auto !important;
        background-color: white !important;
    }

    .cart-button span {
        display: none;
    }

    .cart-button {
        padding: 0;
    }

    .cart-button i {
        font-size: 20px;
    }

    .user-section {
        gap: 15px;
    }

    .profile-dropdown {
        display: none;
    }

    .profile-button {
        display: block !important;
        width: 40px;
        height: 40px;
    }

    .profile-button img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }

    .search-results {
        width: 90vw;
        right: -45vw;
    }

    .login-button {
        width: auto;
        height: auto;
        padding: 8px;
        margin: 0;
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        gap: 8px;
    }

    .login-button .text-container {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        text-align: right;
    }

    .search-box {
        display: none;
    }

    body {
        padding-top: 0 !important; /* Let natural flow handle the spacing */
        padding-bottom: 100px !important;
        background-color: #f0f2f5;
    }
}

@media (max-width: 480px) {
    .navbar {
        padding: 8px 10px;
        height: auto;
        min-height: 60px;
    }

    .logo-container {
        width: 136px;
        height: 136px;
    }

    .cart-button {
        font-size: 14px;
    }

    .search-box.active {
        width: 180px;
    }

    body {
        padding-bottom: 60px;
    }
}

/* استایل‌های مشترک برای نتایج جستجو در موبایل و دسکتاپ */
.mobile-search-results .search-section,
.search-results .search-section {
    margin-bottom: 15px;
}

.mobile-search-results .search-section h3,
.search-results .search-section h3 {
    padding: 8px 10px;
    background-color: #f5f5f5;
    margin: 0;
    font-size: 14px;
    font-weight: bold;
}

.mobile-search-results .search-section ul,
.search-results .search-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-search-results .search-item,
.search-results .search-item {
    display: flex;
    align-items: center;
    padding: 10px;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #eee;
}

.mobile-search-results .search-item:hover,
.search-results .search-item:hover {
    background-color: #f9f9f9;
}

.mobile-search-results .search-result-image,
.search-results .search-result-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    margin-left: 10px;
}

.mobile-search-results .search-result-no-image,
.search-results .search-result-no-image {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-left: 10px;
}

.mobile-search-results .search-item-details,
.search-results .search-item-details {
    flex: 1;
}

.mobile-search-results .search-item-name,
.search-results .search-item-name {
    display: block;
    font-weight: bold;
    margin-bottom: 3px;
}

.mobile-search-results .search-item-price,
.search-results .search-item-price {
    display: block;
    font-size: 13px;
    color: #666;
}

.mobile-search-results .view-all-results,
.search-results .view-all-results {
    text-align: center;
    padding: 10px;
    border-top: 1px solid #eee;
}

.mobile-search-results .view-all-results a,
.search-results .view-all-results a {
    color: #4a90e2;
    text-decoration: none;
    font-size: 14px;
}

.mobile-search-results .no-results,
.search-results .no-results {
    padding: 15px;
    text-align: center;
    color: #666;
}

/* Mobile Menu Final Styles */
@media (max-width: 768px) {
    /* Base styles for the mobile menu, preparing for animation */
    .mobile-menu {
        /* Use visibility/opacity for smooth transitions */
        visibility: hidden;
        opacity: 0;
        /* Start smaller for a pop-in effect */
        transform: scale(0.95);
        transform-origin: top right; /* Animate from the top-right corner */
        transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s;
        
        /* Positioning and Sizing */
        position: fixed;
        top: 85px; /* Position below the header */
        right: 15px; /* Align to the right */
        width: 75vw; /* Adjust width */
        max-width: 280px; /* Set a smaller max-width */
        height: auto;
        max-height: calc(100vh - 180px); /* Ensure it fits on screen */
        
        /* Appearance */
        background: white;
        padding: 20px;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        z-index: 2000;
        overflow-y: auto;
        
        /* Override desktop styles */
        left: auto;
        display: flex;
        flex-direction: column;
    }

    /* Styles for the active (visible) mobile menu */
    .mobile-menu.active {
        visibility: visible;
        opacity: 1;
        transform: scale(1); /* Final animation state */
        /* Add a subtle shadow on the right to indicate potential horizontal scroll */
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), inset -10px 0 10px -10px rgba(0, 0, 0, 0.2);
    }

    /* Ensure the bottom nav bar is behind the overlay but above content */
    .mobile-bottom-nav {
        z-index: 1990;
    }
}

/* Social Media Links for Mobile View */
.mobile-social-links {
    /* Positioning */
    position: fixed;
    top: 50%;
    /* Start off-screen to the left */
    left: -60px; 
    transform: translateY(-50%);
    z-index: 2001; /* Above the menu */
    
    /* Layout */
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    /* Appearance */
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    
    /* Animation */
    opacity: 0;
    visibility: hidden;
    transition: left 0.4s ease, opacity 0.4s ease, visibility 0.4s;
}

.mobile-social-links.active {
    left: 15px; /* Slide in to view */
    opacity: 1;
    visibility: visible;
    transition-delay: 0.2s; /* Delay to sync with menu opening */
}

.mobile-social-links .social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    font-size: 20px;
    text-decoration: none;
    transition: transform 0.2s ease;
}

.mobile-social-links .social-icon:hover {
    transform: scale(1.15);
}

.mobile-social-links .social-icon.support {
    background-color: #5a67d8;
}
.mobile-social-links .social-icon.whatsapp {
    background-color: #25D366;
}
.mobile-social-links .social-icon.telegram {
    background-color: #0088cc;
}
.mobile-social-links .social-icon.instagram {
    background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%,#d6249f 60%,#285AEB 90%);
}
