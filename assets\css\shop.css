:root {
    --card-margin: 10px;
    /* این مقدار رو می‌تونید تغییر بدید */
}

/* تعریف فونت سمیم */
@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('../fonts/Samim.eot');
    src: url('../fonts/Samim.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Samim.woff2') format('woff2'),
        url('../fonts/Samim.woff') format('woff'),
        url('../fonts/Samim.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* اعمال فونت به همه المان‌ها */
body,
button,
input,
select,
textarea,
label,
a,
p,
h1,
h2,
h3,
h4,
h5,
h6,
span,
div {
    font-family: '<PERSON><PERSON>', tahoma, <PERSON><PERSON>, sans-serif !important;
}

.category-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #000;
    font-family: '<PERSON><PERSON>', tahoma, Arial, sans-serif;
}

.sidebar-search-box {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 1rem;
    padding: 0.35rem 0.75rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 250px;
}

.sidebar-search-box input {
    flex-grow: 1;
    border: none;
    outline: none;
    font-size: 0.9rem;
    color: #333;
    background-color: transparent;
    padding: 0.35rem;
    width: 100%;
}

.btn-active {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
    position: relative;
    z-index: 1;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(59, 130, 246, 0.3) !important;
}

.product-card {
    border: 1px solid #ddd;
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    overflow: hidden;
    opacity: 1;
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-card:hover img {
    transform: scale(1.05);

}

.product-card img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-image-container {
    position: relative;
    width: 100%;
    padding-top: 75%;
    overflow: hidden;
    border-radius: 0.75rem;
}

.out-of-stock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.out-of-stock-text {
    background: rgba(229, 62, 62, 0.8);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: bold;
    transform: rotate(-15deg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    font-family: 'IRANSans', sans-serif !important;
}

.product-card.out-of-stock {
    opacity: 0.8;
}

.product-card.out-of-stock:hover {
    opacity: 1;
}

product-card.out-of-stock .add-to-cart {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.product-card.out-of-stock .add-to-cart {
    content: none;
    font-size: 0;
}

.product-card.out-of-stock .add-to-cart::before {
    content: 'ناموجود در انبار';
    font-size: 14px;
    color: #e53e3e;
}

/* مخفی کردن نوشته اضافی */
.product-card.out-of-stock .product-out-of-stock,
.product-card.out-of-stock .add-to-cart span {
    display: none;
}

.product-card .product-name {
    font-size: 1.25rem;
    font-weight: bold;
    margin-top: 1rem;
    color: #000000;
}

.product-card .product-description {
    color: #555;
    margin-top: 0.5rem;
    color: #666666;
}

.product-card .product-price {
    color: #38a169;
    font-weight: bold;
    margin-top: 1rem;
}

.product-card .product-discount-price {
    color: #38a169;
    font-weight: bold;
    margin-top: 1rem;
}

.product-card .product-old-price {
    text-decoration: line-through;
    margin-left: 1rem;
    color: #e53e3e;
    text-decoration: line-through;
    text-decoration-color: #e53e3e;
    font-weight: 500;
}

.product-card .product-stock {
    font-size: 1rem;
    color: #4a90e2;
    margin-top: 1rem;
    color: rgb(255, 0, 0);
}

.product-card .product-out-of-stock {
    font-size: 1rem;
    color: red;
    font-weight: bold;
    margin-top: 1rem;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e53e3e;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    z-index: 1;
}

@media (max-width: 768px) {
    .discount-badge {
        font-size: 0.7rem;
        padding: 0.1rem 0.25rem;
        top: 0.25rem;
        right: 0.25rem;
        z-index: 2;
        border-radius: 0.5rem;
    }
}

.stock-low {
    background-color: #ecc94b;
}

.stock-out {
    background-color: #e53e3e;
}

/* Add skeleton loading animation */
@keyframes loading {
    0% {
        background-position: 200% 0;
    }

    100% {
        background-position: -200% 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.product-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 1rem;
}

.add-to-cart {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    font-weight: 500 !important;
    padding: 0.35rem 0.75rem !important;
    font-size: 0.85rem !important;
    width: auto !important;
    min-width: 100px !important;
    height: 32px !important;
    border-radius: 0.4rem !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2), 0 1px 2px rgba(0, 0, 0, 0.08) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.35rem !important;
    margin: 0 !important;
    border: none !important;
    cursor: pointer !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease !important;
    text-decoration: none !important;
    outline: none !important;
    position: relative !important;
    overflow: hidden !important;
}

.add-to-cart:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
}

.add-to-cart:active {
    transform: translateY(1px) !important;
    box-shadow: 0 1px 2px rgba(37, 99, 235, 0.2), 0 1px 1px rgba(0, 0, 0, 0.08) !important;
    background: linear-gradient(135deg, #1d4ed8 0%, #172554 100%) !important;
}





.price-filter-container {
    font-family: inherit;
}

.price-range-slider {
    position: relative;
    height: 5px;
    margin: 30px 0;
}

.slider-track {
    position: absolute;
    width: 100%;
    height: 5px;
    background: #e0e0e0;
    border-radius: 3px;
}

.slider-track::before {
    content: '';
    position: absolute;
    height: 100%;
    background: #3b82f6;
    border-radius: 3px;
}

input[type="range"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    position: absolute;
    width: 100%;
    height: 5px;
    background: none;
    pointer-events: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    pointer-events: auto;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #fff;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    pointer-events: auto;
    cursor: pointer;
}

.price-input-group input {
    direction: ltr;
    padding-left: 60px;
}

.ltr {
    direction: ltr;
}

/* اضافه کردن استایل‌های hover */
input[type="range"]::-webkit-slider-thumb:hover {
    background: #3b82f6;
}

input[type="range"]::-moz-range-thumb:hover {
    background: #3b82f6;
}

.btn-primary:hover {
    opacity: 0.9;
}

.price-filter select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: #fff url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E") no-repeat right 0.5rem center;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.compact-select {
    @apply appearance-none bg-gray-50 border border-gray-200 text-sm rounded px-3 py-2 ml-1;
    min-width: 180px;
    /* افزایش عرض برای نمایش بهتر اعداد */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 0.5rem center;
    padding-left: 2rem;
    direction: rtl;
    font-size: 0.95rem;
}

.filter-btn {
    @apply p-2 rounded-md transition-all duration-200 transform hover: scale-110;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-btn.apply {
    @apply bg-green-500 text-white hover: bg-green-600;
}

.filter-btn.clear {
    @apply bg-red-500 text-white hover: bg-red-600;
}

.compact-select:focus {
    @apply outline-none ring-2 ring-blue-500 ring-opacity-50;
}

@media (max-width: 768px) {
    .container.mx-auto.p-12 {
        padding: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
        margin: 0 auto !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .grid.grid-cols-4 {
        width: 100% !important;
        max-width: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
        padding: 0 !important;
    }

    .product-grid {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 1.25rem !important;
        padding: 0.5rem !important;
    }

    .product-card-wrapper {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        display: block !important;
    }

    .product-card {
        min-height: auto !important;
        width: 100% !important;
        max-width: none !important;
        border-radius: 0.75rem !important;
        display: flex !important;
        flex-direction: row !important;
        padding: 0.75rem !important;
        margin: 0 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
        background: white !important;
        height: 220px !important;
    }

    .product-image-container {
        position: relative !important;
        width: 100px !important;
        height: 200px !important;
        flex-shrink: 0 !important;
        border-radius: 0.5rem !important;
        overflow: hidden !important;
        margin-left: 0.75rem !important;
    }

    .product-image-container img {
        position: absolute !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
    }

    .product-content {
        display: flex !important;
        flex-direction: column !important;
        flex: 1 !important;
        width: calc(100% - 110px) !important;
        padding: 0.25rem !important;
        justify-content: space-between !important;
        height: 200px !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .product-card .product-name {
        display: -webkit-box;
        font-size: 0.9rem;
        height: 44px;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
        margin-top: 0;
        text-align: right;
        padding: 0 2px;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.75rem;
        width: calc(100% - 4px);
        direction: rtl;
        position: relative;
        background: none !important;
    }

    .product-card .product-description {
        display: -webkit-box;
        font-size: 0.75rem;
        height: 40px;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
        color: #666;
        margin-bottom: 0.75rem;
        text-align: right;
        padding: 0 2px;
        width: calc(100% - 4px);
        direction: rtl;
        position: relative;
        background: none !important;
    }

    .product-card .product-price {
        font-size: 0.85rem;
        margin-top: 0.25rem;
        text-align: right;
        min-height: auto;
        padding: 0;
        color: #000;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }

    .product-card .product-discount-price {
        font-size: 0.85rem;
        margin-top: 0.25rem;
        text-align: right;
        min-height: auto;
        padding: 0;
        color: #000;
        font-weight: bold;
    }

    .product-card .product-old-price {
        font-size: 0.75rem;
        color: #e53e3e;
        text-decoration: line-through;
        text-decoration-color: #e53e3e;
        margin-right: 0.5rem;
        font-weight: 500;
    }



    /* نمایش متن "تنها X عدد در انبار باقی مانده" */
    .product-card .product-stock {
        font-size: 0.75rem;
        color: #ff0000;
        margin-top: 0.25rem;
        text-align: right;
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }

    .price-filter {
        @apply flex-wrap justify-center;
    }

    .compact-select {
        min-width: 150px;
    }

    .price-filter {
        margin: 0.25rem -0.5rem 0;
        padding: 0.8rem;
        background: #f9fafb;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        max-width: calc(100% + 1rem);
        width: calc(100% + 1rem);
    }

    .mobile-filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .mobile-filter-content {
        display: flex;
        align-items: center;
    }

    .price-range-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .price-label {
        font-size: 0.875rem;
        color: #4b5563;
        font-weight: 500;
    }

    .compact-select {
        min-width: auto;
        width: 100%;
        padding: 0.5rem;
        font-size: 0.813rem;
        border-radius: 0.375rem;
        background-position: left 0.25rem center;
        background-size: 1rem;
        padding-left: 1.5rem;
    }

    .compact-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }

    .filter-btn {
        background: initial !important;
        box-shadow: initial !important;
        transform: initial !important;
        min-width: 24px !important;
        min-height: 24px !important;
        padding: 0.2rem !important;
        border-radius: 0.375rem !important;
    }

    .filter-btn svg {
        width: 24px !important;
        height: 24px !important;
        stroke-width: 2px !important;
    }

    .filter-btn.apply {
        background: initial !important;
        box-shadow: initial !important;
        @apply bg-green-500 text-white hover: bg-green-600 !important;
    }

    .filter-btn.clear {
        background: initial !important;
        box-shadow: initial !important;
        @apply bg-red-500 text-white hover: bg-red-600 !important;
    }

    .filter-btn:hover,
    .filter-btn:active {
        transform: scale(1.1) !important;
    }

    .price-filter .flex {
        gap: 0.5rem !important;
    }

    .compact-select {
        height: 40px !important;
        border-radius: 0.375rem !important;
    }

    .sidebar-search-box {
        margin: -0.5rem auto 0.5rem;
        width: 100%;
        max-width: 100%;
        border-radius: 0.5rem;
        padding: 0.25rem 0.75rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
        border: 1px solid #e5e7eb;
    }

    .sidebar-search-box input {
        font-size: 0.8rem;
        padding: 0.25rem;
        height: 28px;
    }

    .sidebar-search-box input::placeholder {
        color: #9ca3af;
    }

    .sidebar-search-box:focus-within {
        border-color: #3b82f6;
        box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
    }

    .price-filter,
    .sidebar-search-box {
        animation: fadeIn 0.2s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-5px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .col-span-3 {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    .discount-badge {
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
        top: 0.75rem;
        right: 0.75rem;
    }
}

@media (max-width: 768px) {
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .pagination .btn {
        padding: 0.35rem 0.65rem;
        font-size: 0.813rem;
    }
}

.flex.justify-center.items-center.mt-8.gap-2 {
    display: flex;
    flex-wrap: nowrap;
    gap: 0.35rem;
    padding: 0.35rem 0.25rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
}

.flex.justify-center.items-center.mt-8.gap-2::-webkit-scrollbar {
    display: none;
}

.flex.justify-center.items-center.mt-8.gap-2 .btn {
    flex: 0 0 auto;
    white-space: nowrap;
    padding: 0.35rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%) !important;
    border: 2px solid #dbeafe !important;
    color: #3b82f6 !important;
    font-weight: 500 !important;
}

.flex.justify-center.items-center.mt-8.gap-2 .btn:hover {
    border-color: #3b82f6 !important;
    background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%) !important;
    color: #1e40af !important;
}

.flex.justify-center.items-center.mt-8.gap-2 .btn.btn-active,
.pagination-container .btn.btn-active {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
    position: relative;
    overflow: hidden;
    transform: scale(1.05);
}

.flex.justify-center.items-center.mt-8.gap-2 .btn.btn-active:hover,
.pagination-container .btn.btn-active:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    transform: scale(1.05) translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25) !important;
}

.flex.justify-center.items-center.mt-8.gap-2 .btn.btn-active::before,
.pagination-container .btn.btn-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    animation: shine 2s infinite;
}

.btn-outline.btn-active {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
    position: relative;
    overflow: hidden;
    transform: scale(1.05);
}

.btn-outline.btn-active:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    transform: scale(1.05) translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25) !important;
}

.btn-outline.btn-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    animation: shine 2s infinite;
}

.btn-outline {
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%) !important;
    border: 2px solid #dbeafe !important;
    color: #3b82f6 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.btn-outline:hover {
    border-color: #3b82f6 !important;
    background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%) !important;
    color: #1e40af !important;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }

    20% {
        transform: translateX(100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.category-slider {
    width: 100%;
    overflow-x: hidden;
    position: relative;
    margin-bottom: 0.75rem;
    -webkit-overflow-scrolling: touch;
}

.category-slider .flex {
    display: flex;
    flex-wrap: nowrap;
    gap: 0.35rem;
    padding: 0.35rem 0.25rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
}

.category-slider-title {
    display: none;
}

.category-slider .flex::-webkit-scrollbar {
    display: none;
}

.category-slider .btn {
    flex: 0 0 auto;
    white-space: nowrap;
    padding: 0.35rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
}

@media (max-width: 768px) {
    .category-slider {
        margin: 0 -0.5rem;
        padding: 0 0.5rem;
    }

    .category-slider .flex {
        padding: 0.5rem;
        gap: 0.35rem;
        width: 100%;
    }

    .category-slider .btn {
        padding: 0.35rem 0.65rem;
        font-size: 0.813rem;
        min-width: auto;
    }

    .category-slider::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 20px;
        background: linear-gradient(to left, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .category-slider:hover::after {
        opacity: 1;
    }

    .category-slider-title {
        display: block;
        font-size: 1rem;
        font-weight: 600;
        color: #333;
        margin: 1rem 0.5rem 0.5rem 0.5rem;
        padding: 0 0.5rem;
    }
}

@supports (-webkit-touch-callout: none) {
    .category-slider .flex {
        -webkit-overflow-scrolling: touch;
    }
}

@-moz-document url-prefix() {
    .category-slider .flex {
        scrollbar-width: none;
    }
}

@supports (-ms-ime-align: auto) {
    .category-slider .flex {
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}



.filter-title {
    display: none;
}

/* استایل دکمه افزودن به سبد خرید */
.add-to-cart {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
    width: auto !important;
    min-width: 100px !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2), 0 1px 2px rgba(0, 0, 0, 0.08) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    margin: 0 !important;
    border: none !important;
    cursor: pointer !important;
    text-decoration: none !important;
    outline: none !important;
    position: relative !important;
    overflow: hidden !important;
}

.add-to-cart:hover {
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
}

.add-to-cart:active {
    box-shadow: 0 1px 2px rgba(37, 99, 235, 0.2), 0 1px 1px rgba(0, 0, 0, 0.08) !important;
    background: linear-gradient(135deg, #1d4ed8 0%, #172554 100%) !important;
}

.add-to-cart .IconContainer {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.add-to-cart .cart-icon {
    fill: white;
    height: 1.2em;
    width: 1.2em;
}

.add-to-cart .text {
    color: white !important;
    font-size: 0.85rem !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* استایل دکمه ناموجود در انبار */
.out-of-stock-btn {
    width: auto !important;
    height: 32px !important;
    border: none !important;
    border-radius: 0.4rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #e53e3e !important;
    font-weight: 500 !important;
    background-color: #f8d7da !important;
    cursor: not-allowed !important;
    opacity: 0.9 !important;
    margin-top: auto !important;
    font-family: 'Samim', sans-serif !important;
    font-size: 0.85rem !important;
    padding: 0.35rem 0.75rem !important;
    border: 1px solid #f5c6cb !important;
    transition: all 0.3s ease-in-out !important;
    box-shadow: none !important;
    min-width: 100px !important;
}

/* استایل دکمه فروش ویژه */
.special-sale-badge {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: linear-gradient(135deg, #ff4e50 0%, #f9d423 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {

    .add-to-cart,
    .out-of-stock-btn,
    .special-sale-badge {
        display: none !important;
    }
}



@media (max-width: 768px) {
    .mobile-filter-header {
        display: none;
    }

    .filter-title {
        display: none;
    }
}

.quick-view-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
}

.product-card:hover .quick-view-btn {
    opacity: 1;
}

.btn-outline.btn-info {
    color: #000000;
    border-color: #e5e7eb;
    background-color: transparent;
}

.btn-outline.btn-info:hover {
    background-color: #f3f4f6;
    border-color: #e5e7eb;
    color: #000000;
}

.btn-outline.btn-info.btn-active {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
}

.btn-outline.btn-info.btn-active:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25) !important;
}



.product-card {
    background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
    border: 1px solid #e5e7eb !important;
    transition: all 0.3s ease !important;
    animation: scaleIn 0.5s ease-out !important;
}

.product-card:hover {
    border-color: #d1d5db !important;
    box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
    }

    to {
        transform: scale(1);
    }
}

.btn-outline.btn-info {
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%) !important;
    border: 2px solid #dbeafe !important;
    color: #3b82f6 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.btn-outline.btn-info:hover {
    border-color: #3b82f6 !important;
    background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 100%) !important;
    color: #1e40af !important;
}

.btn-outline.btn-info.btn-active {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
}

.btn-outline.btn-info.btn-active:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25) !important;
}

.sidebar-search-box {
    background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
    border: 1px solid #e5e7eb !important;
    transition: all 0.3s ease !important;
}

.sidebar-search-box:focus-within {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

.swal2-popup {
    border-radius: 1rem !important;
    padding: 2rem !important;
    font-family: 'Samim', sans-serif !important;
}

.swal2-title {
    color: #1e293b !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
    font-family: 'Samim', sans-serif !important;
}

.swal2-html-container {
    color: #64748b !important;
    font-size: 1rem !important;
    margin-bottom: 1.5rem !important;
    font-family: 'Samim', sans-serif !important;
}

.swal2-actions {
    gap: 0.5rem !important;
}

.swal2-confirm {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border-radius: 0.75rem !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    font-family: 'Samim', sans-serif !important;
}

.swal2-confirm:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3) !important;
}

.swal2-cancel {
    background: linear-gradient(135deg, #6b7280 0%, #374151 100%) !important;
    border-radius: 0.75rem !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    font-family: 'Samim', sans-serif !important;
}

.swal2-cancel:hover {
    background: linear-gradient(135deg, #4b5563 0%, #1f2937 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px rgba(75, 85, 99, 0.3) !important;
}

.swal2-actions button {
    font-size: 0.875rem !important;
    border: none !important;
    cursor: pointer !important;
}

.swal2-toast {
    border-radius: 0.75rem !important;
    padding: 1rem !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    font-family: 'Samim', sans-serif !important;
}

.swal2-toast .swal2-title {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
}

.swal2-toast .swal2-html-container {
    font-size: 0.875rem !important;
    margin-bottom: 0 !important;
}

.swal2-toast .swal2-actions {
    margin-top: 0.5rem !important;
}

.swal2-toast .swal2-confirm,
.swal2-toast .swal2-cancel {
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem !important;
}

/* تعریف انیمیشن shine برای افکت چشمک زدن دکمه‌های فعال */
@keyframes shine {
    0% {
        transform: translateX(-100%);
    }

    20% {
        transform: translateX(100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.9);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-5px) scale(1.05);
}

.scroll-to-top svg {
    margin: 0 auto;
    width: 24px;
    height: 24px;
    stroke-width: 3;
}

@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 80px;
        right: 20px;
        width: 50px;
        height: 50px;
    }

    .scroll-to-top svg {
        width: 20px;
        height: 20px;
    }
}

/* استایل برای جلوگیری از اسکرول صفحه هنگام باز بودن مدال */
body.modal-open {
    overflow: hidden;
}

/* استایل‌های مدال انتخاب قیمت */
.price-select-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.price-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.price-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 320px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    direction: rtl;
    padding: 20px;
}

.price-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #3b82f6;
    text-align: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f9ff;
}

.price-modal-close {
    position: absolute;
    top: 10px;
    left: 10px;
    background: none;
    border: none;
    font-size: 24px;
    color: #64748b;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.price-modal-close:hover {
    background-color: #f1f5f9;
    color: #ef4444;
}

.price-modal-options {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.price-modal-option {
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.price-modal-option:hover {
    background-color: #eff6ff;
}

.price-modal-option.selected {
    background-color: #dbeafe;
    color: #1e40af;
    font-weight: 600;
}

/* استایل انیمیشن بارگذاری */
.loading-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.loading-text {
    font-size: 16px;
    color: #3b82f6;
    font-weight: 600;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* استایل دکمه افزودن به سبد خرید */
.add-to-cart {
    width: 100%;
    height: 40px;
    border-radius: 12px;
    border: none;
    background-color: #3b82f6 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.103);
    position: relative;
    font-family: 'IRANSans', sans-serif !important;
    direction: rtl !important;
    gap: 4px;
}

.add-to-cart .IconContainer {
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-to-cart .cart-icon {
    fill: white;
    height: 1.2em;
    width: 1.2em;
}

.add-to-cart .text {
    height: 100%;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px !important;
    font-weight: 600;
    margin: 0;
    padding: 0;
}

.add-to-cart:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.add-to-cart:active {
    background: linear-gradient(135deg, #1d4ed8 0%, #172554 100%) !important;
}

@media (max-width: 768px) {
    .add-to-cart {
        height: 35px;
        font-size: 11px !important;
    }

    .add-to-cart .text {
        font-size: 11px !important;
    }
}

/* استایل برای اسم محصول */
.product-name {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    direction: rtl;
}

/* استایل برای اسم محصول انگلیسی */
.product-name-en {
    direction: ltr !important;
    text-align: left !important;
}

/* استایل برای توضیحات محصول */
.product-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5;
}

/* استایل دکمه ناموجود */
.out-of-stock-btn {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e53e3e !important;
    /* رنگ قرمز برای متن */
    font-weight: 600 !important;
    background-color: #f8d7da !important;
    /* پس‌زمینه صورتی کمرنگ */
    cursor: not-allowed;
    opacity: 0.9;
    margin-top: auto;
    font-family: 'IRANSans', sans-serif !important;
    font-size: 12px !important;
    border: 1px solid #f5c6cb !important;
    /* حاشیه صورتی کمرنگ‌تر */
    transition: all 0.3s ease-in-out;
    box-shadow: none !important;
    /* حذف سایه */
    border-radius: 8px;
}

@media (max-width: 768px) {
    .out-of-stock-btn {
        height: 35px;
        font-size: 11px !important;
        font-weight: 500 !important;
    }
}

/* استایل برای قیمت محصول */
.product-price {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: auto;
    font-family: 'IRANSans', sans-serif;
    direction: rtl;
}

.product-old-price {
    color: #999;
    text-decoration: line-through;
    font-size: 12px;
    display: block;
}

.product-current-price {
    color: #021959;
    /* تغییر رنگ به آبی تیره #021959 (R=2 G=25 B=89) */
    font-weight: 600;
    font-size: 14px;
    display: block;
}

/* تغییر رنگ قیمت در کارت محصول */
.product-card .product-price {
    color: #021959;
    /* تغییر رنگ به آبی تیره #021959 (R=2 G=25 B=89) */
    font-weight: bold;
    margin-top: 1rem;
}

.product-card .product-discount-price {
    color: #021959;
    /* تغییر رنگ به آبی تیره #021959 (R=2 G=25 B=89) */
    font-weight: bold;
    margin-top: 1rem;
}

.btn-outline.btn-info {
    color: #3b82f6;
    border-color: #3b82f6;
}

.btn-outline.btn-info:hover {
    background-color: #3b82f6;
    color: white;
}

.btn-outline.btn-info.btn-active {
    background-color: #3b82f6;
    color: white;
}

.filter-btn {
    background-color: transparent;
    color: #4b5563;
    padding: 0.5rem;
    border-radius: 0.5rem;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    outline: none !important;
}

.filter-btn:active {
    border: 1px solid #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

@media (max-width: 768px) {
    .filter-btn {
        padding: 0.3rem;
    }

    .filter-btn svg {
        width: 18px;
        height: 18px;
    }
}

.filter-btn.apply {
    color: #3b82f6;
}

.filter-btn.apply:hover {
    color: #2563eb;
    background-color: transparent;
    border-color: transparent;
    transform: scale(1.1);
}

.filter-btn.clear {
    color: #ef4444;
}

.filter-btn.clear:hover {
    color: #dc2626;
    background-color: transparent;
    border-color: transparent;
    transform: scale(1.1);
}

.filter-btn svg {
    width: 20px;
    height: 20px;
}

/* استایل جدید برای منوهای کشویی */
.custom-select-container {
    position: relative;
    width: auto;
}

/* استایل برای فیلتر قیمت دسکتاپ */
@media (min-width: 769px) {
    .price-filter .price-inputs {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
    }
    
    .price-filter .price-select-container {
        position: relative;
        min-width: 140px;
    }
    
    .price-filter .price-select-input {
        width: 100%;
        padding: 0.5rem 1rem;
        border: 1px solid #dbeafe;
        border-radius: 0.5rem;
        background-color: white;
        font-size: 0.95rem;
        transition: all 0.3s;
        color: #1e3a8a;
        cursor: pointer;
        text-align: right;
        direction: rtl;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233b82f6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: left 0.5rem center;
        background-size: 1rem;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 42px;
    }
    
    .price-filter .price-select-input:hover {
        border-color: #93c5fd;
        background-color: #f8fafc;
    }
    
    .price-filter .price-select-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }
}

.custom-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: #fff;
    border: 1px solid #dbeafe;
    border-radius: 0.5rem;
    padding: 0.5rem 2rem 0.5rem 1rem;
    font-size: 0.95rem;
    color: #1e3a8a;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233b82f6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 0.5rem center;
    background-size: 1rem;
    width: 100%;
    direction: rtl;
    min-width: 140px;
}

.custom-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
}

.custom-select:hover {
    border-color: #93c5fd;
    background-color: #f8fafc;
}

.custom-select option {
    padding: 0.75rem;
    background-color: white;
    color: #1e293b;
}

.custom-select option:hover {
    background-color: #dbeafe;
}

.custom-select option:checked {
    background-color: #3b82f6;
    color: white;
    font-weight: bold;
}

/* استایل برای select با گزینه انتخاب شده */
.custom-select.has-value {
    border-color: #3b82f6;
    background-color: #eff6ff;
    color: #1e40af;
    font-weight: 600;
}

.sorting-container {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.sorting-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border-color: #d1d5db;
}

.filter-btn {
    background-color: transparent;
    color: #4b5563;
    padding: 0.5rem;
    border-radius: 0.5rem;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.filter-btn.apply {
    color: #3b82f6;
}

.filter-btn.apply:hover {
    background-color: #eff6ff;
    border-color: #93c5fd;
}

.filter-btn.clear {
    color: #ef4444;
}

.filter-btn.clear:hover {
    background-color: #fef2f2;
    border-color: #fca5a5;
}

@media (max-width: 768px) {
    .custom-select {
        font-size: 0.875rem;
        padding: 0.4rem 1.5rem 0.4rem 0.75rem;
        background-size: 0.875rem;
        min-width: 120px;
    }

    .mobile-filter-content {
        padding: 0.25rem;
        margin: 0 auto;
        max-width: 100%;
        display: flex;
        justify-content: center;
        width: 100%;
    }
}

/* استایل کامل جدید برای منوی کشویی سفارشی */
.price-select-container {
    position: relative;
    width: 100%;
}

.price-select-input {
    width: 100%;
    padding: 0.5rem 1rem;
    border: 1px solid #dbeafe;
    border-radius: 0.5rem;
    background-color: white;
    font-size: 0.95rem;
    transition: all 0.3s;
    color: #1e3a8a;
    cursor: pointer;
    text-align: right;
    direction: rtl;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233b82f6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 0.5rem center;
    background-size: 1rem;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    min-height: 42px;
}

.price-select-input[data-placeholder]:empty::before {
    content: attr(data-placeholder);
    color: #9ca3af;
}

.price-select-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.price-select-dropdown {
    position: absolute;
    top: 100%;
    left: -10px;
    right: -10px;
    background-color: white;
    border: 1px solid #dbeafe;
    border-radius: 0.5rem;
    margin-top: 0.25rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    display: none;
    min-width: 140px;
    width: auto;
}

@media (max-width: 768px) {
    .price-select-dropdown {
        position: fixed;
        top: auto;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        width: 80%;
        max-width: 250px;
        z-index: 9999;
        margin-top: 5px;
        max-height: 40vh;
        /* محدود کردن ارتفاع به 40% ارتفاع صفحه */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        /* برای اسکرول نرم در iOS */
        background-color: white;
        border: 1px solid #bfdbfe;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        /* تنظیمات برای جلوگیری از جدا شدن منو هنگام اسکرول */
        position: fixed !important;
        transform: translateX(-50%) !important;
    }

    .mobile-price-filter-box .price-select-container {
        position: static;
    }

    .price-select-option {
        padding: 0.8rem 1rem;
        border-bottom: 1px solid #f0f9ff;
        font-size: 0.9rem;
    }

    .price-select-option:last-child {
        border-bottom: none;
    }
}

.price-select-option {
    padding: 0.6rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
    text-align: right;
    direction: rtl;
    font-size: 0.85rem;
    white-space: nowrap;
    border-bottom: 1px solid #f0f9ff;
}

.price-select-option:hover {
    background-color: #eff6ff;
}

.price-select-option.selected {
    background-color: #dbeafe;
    color: #1e40af;
    font-weight: bold;
}

.price-inputs {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
}

@media (max-width: 768px) {
    .price-inputs {
        gap: 0.35rem;
        max-width: 100%;
        margin: 0 auto;
        width: 100%;
    }
}

.submit-price-filters {
    display: flex;
    margin-top: 0.5rem;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .submit-price-filters {
        justify-content: center;
        margin: 0.5rem auto 0;
        max-width: 100%;
        width: 100%;
    }

    .mobile-price-filter-box {
        background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e5e7eb;
        padding: 1.2rem 0.75rem;
        margin: 0.5rem -0.5rem 1rem;
        max-width: calc(100% + 1rem);
        width: calc(100% + 1rem);
        box-sizing: border-box;
        overflow: hidden;
    }

    .mobile-filter-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #3b82f6;
        text-align: center;
        margin-bottom: 0.7rem;
        padding-bottom: 0.7rem;
        border-bottom: 1px solid #f0f9ff;
    }

    .price-filter {
        background-color: transparent;
        border: none;
        box-shadow: none;
        margin: 0;
        padding: 0;
    }
}

@media (max-width: 768px) {
    .price-select-input {
        font-size: 0.9rem;
        padding: 0.5rem 0.7rem;
        border-radius: 10px;
        text-align: center;
        height: 40px;
        min-width: 110px;
        max-width: 110px;
        border-color: #dbeafe;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
    }

    .price-select-container {
        width: 100%;
        flex: 1;
    }

    .mobile-price-filter-box .filter-btn {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    }

    .mobile-price-filter-box .filter-btn.apply {
        background-color: #f0f9ff;
        border: 1px solid #bfdbfe;
    }

    .mobile-price-filter-box .filter-btn.apply svg {
        color: #3b82f6;
    }

    .mobile-price-filter-box .filter-btn.clear {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
    }

    .mobile-price-filter-box .filter-btn.clear svg {
        color: #ef4444;
    }

    .mobile-price-filter-box .filter-btn:hover {
        transform: scale(1.05);
    }

    .mobile-price-filter-box .filter-btn:active {
        transform: scale(0.95);
    }
}

/* استایل برای دکمه‌های صفحه‌بندی فعال */
.btn-outline.btn-active {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

/* استایل‌های اضافی برای اطمینان از عملکرد کلاس btn-active */
.pagination-container .btn-outline.btn-active {
    background-color: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* استایل دکمه جستجو */
.search-button {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* تغییر موقعیت دکمه جستجو در حالت موبایل */
@media (max-width: 768px) {
    .sidebar-search-box.md\:hidden .search-button {
        left: calc(5% + 10px);
        width: 24px;
        height: 24px;
    }
}

.search-button:hover {
    background-color: #2563eb;
    transform: translateY(-50%) scale(1.1);
}

.sidebar-search-box {
    position: relative;
}

/* استایل برای جعبه جستجو در حالت موبایل */
@media (max-width: 768px) {
    .sidebar-search-box.md\:hidden {
        display: flex;
        justify-content: center;
        align-items: center;
        width: calc(100% + 1rem);
        max-width: calc(100% + 1rem);
        margin: 0.5rem -0.5rem 1rem;
        padding: 0;
        position: relative;
        background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
        border: 1px solid #e5e7eb;
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .sidebar-search-box.md\:hidden input {
        width: 100%;
        max-width: 100%;
        border-radius: 8px;
        padding: 10px 15px 10px 40px;
        font-size: 14px;
        text-align: center;
        height: 45px;
    }
}

.toman-svg-inline {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    transform: scale(1.3);
    transform-origin: center;
    margin: 0 2px;
}

.toman-svg-inline svg {
    width: 100%;
    height: 100%;
    display: inline-block;
}

/* استایل خاص برای SVG تومان در قیمت محصولات */
.product-price .toman-svg-inline {
    transform: scale(1.4);
}

.product-current-price .toman-svg-inline,
.product-old-price .toman-svg-inline {
    transform: scale(1.4);
}

/* استایل دکمه فیلتر تخفیف */
.discount-filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #dbeafe;
    background-color: #fff;
    color: #3b82f6;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.discount-filter-btn:hover {
    background-color: #eff6ff;
    border-color: #93c5fd;
    transform: translateY(-1px);
}

.discount-filter-btn.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    border-color: transparent;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

/* استایل برای دکمه‌های فیلتر موبایل */
.mobile-filter-buttons {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.mobile-filter-buttons .discount-filter-btn {
    flex: 1;
    text-align: center;
    font-size: 0.8rem;
    padding: 0.6rem 0.5rem;
}