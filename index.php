<?php
// بررسی می‌کنیم که آیا کوکی bypass وجود داره یا نه
// if (!isset($_COOKIE['bypass'])) {
    // اگر کوکی موجود نبود، صفحه بروزرسانی نمایش داده می‌شود
//    header("Location: maintenance.html");
//     exit();
// }

// استفاده از سیستم "مرا به خاطر بسپار" و بررسی اتوماتیک وضعیت لاگین
include("auth_check.php");
require_once 'redirect-clean-url.php';
// اگر کوکی موجود بود، سایت نمایش داده می‌شود
// بقیه کدهای سایت شما در اینجا قرار می‌گیرند
?>

<?php
if (isset($_GET['url']) && $_GET['url'] === 'home') {
    header("HTTP/1.1 301 Moved Permanently");
    header("Location: /");
    exit();
}
?>


<?php

include("conn.php");

// نرخ تبدیل درهم به تومان - باید با فایل‌های دیگر هماهنگ باشد
$aed_to_toman_rate = 16000;

// تعریف محصولات و عناوین و دسته بندی برای هر اسلاید (قابل تغییر)
$slide_details = [
    1 => [                      //برای اسلاید
        'title' => 'آیفون',    //برای تایتل
        'category' => 'آیفون'  // برای دسته بندی
    ],
    2 => [
        'title' => 'گیمینگ',
        'category' => 'گیمینگ'
    ],
    3 => [
        'title' => 'ایپد',
        'category' => 'ایپد'
    ],
    4 => [
        'title' => 'اسپیکر',
        'category' => 'اسپیکر'
    ],
    5 => [
        'title' => ' اپل واچ',
        'category' => 'اپل واچ'
    ],
    6 => [
        'title' => 'اکسسوری و لوازم جانبی',
        'category' => 'اکسسوری و لوازم جانبی'
    ],
    7 => [
        'title' => 'ایرپاد',
        'category' => 'ایرپاد'
    ]
];

// دریافت پارامتر دسته بندی از URL
$category = isset($_GET['category']) ? $_GET['category'] : '';

// عنوان صفحه بر اساس دسته بندی
$page_title = "فروشگاه";
if (!empty($category)) {
    $page_title .= " - " . htmlspecialchars($category);
}

// تنظیم کوئری برای فیلتر کردن محصولات
$where_clause = '';
if (!empty($category)) {
    $category = $conn->real_escape_string($category);
    $where_clause = "WHERE p.category = '$category'";
}

// کوئری دریافت محصولات
$product_query = "SELECT p.*,
                 (SELECT image FROM product_images WHERE product_id = p.id LIMIT 1) as first_image
                 FROM products p
                 $where_clause
                 ORDER BY p.created_at DESC";

$product_result = $conn->query($product_query);

// تعریف دسته‌بندی برای چهار کارت پایین صفحه (بدون نمایش عنوان)
$card_categories = [
    1 => 'اسپیکر',
    2 => 'آیفون',
    3 => 'گیمینگ',
    4 => 'اسپیکر'
];

// دریافت پارامتر دسته بندی از URL
$category = isset($_GET['category']) ? $_GET['category'] : '';

// عنوان صفحه بر اساس دسته بندی
$page_title = "فروشگاه";
if (!empty($category)) {
    $page_title .= " - " . htmlspecialchars($category);
}

// تنظیم کوئری برای فیلتر کردن محصولات
$where_clause = '';
if (!empty($category)) {
    $category = $conn->real_escape_string($category);
    $where_clause = "WHERE p.category = '$category'";
}

// کوئری دریافت محصولات
$product_query = "SELECT p.*,
                 (SELECT image FROM product_images WHERE product_id = p.id LIMIT 1) as first_image
                 FROM products p
                 $where_clause
                 ORDER BY p.created_at DESC";

$product_result = $conn->query($product_query);
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">

<head>
  <!-- زبان و کدینگ -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <!-- عنوان و توضیحات -->
  <title>سیب موبایل شیراز | فروشگاه اینترنتی آیفون و لوازم جانبی</title>
  <meta name="description" content="خرید انواع گوشی آیفون، قاب، لوازم جانبی و گجت از سیب موبایل شیراز با بهترین قیمت و ضمانت اصالت.">

  <!-- Favicon و آیکون اپل -->
  <link rel="icon" href="/image/logo001.png" type="image/png">
  <link rel="apple-touch-icon" href="/image/logo001.png">

  <!-- Open Graph (برای شبکه‌های اجتماعی) -->
  <meta property="og:title" content="سیب موبایل شیراز | فروشگاه اینترنتی آیفون و لوازم جانبی">
  <meta property="og:description" content="بهترین فروشگاه خرید آیفون، قاب و گجت در شیراز با ارسال سریع.">
  <meta property="og:image" content="https://sibmobileshiraz.ir/image/logo01.png">
  <meta property="og:url" content="https://sibmobileshiraz.ir">
  <meta property="og:type" content="website">

  <!-- Structured Data (برای نمایش لوگو در نتایج گوگل) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "سیب موبایل شیراز",
    "url": "https://sibmobileshiraz.ir",
    "logo": "https://sibmobileshiraz.ir/image/logo001.png"
  }
  </script>

  <!-- لینک استایل‌ها -->
  <link href="assets/css/daisy.css" rel="stylesheet" type="text/css" />
  <link rel="stylesheet" href="assets/css/swiper-bundle.min.css" />

  <!-- اسکریپت‌ها -->
  <script src="assets/js/sweetalert.js"></script>

    <?php include("navbar.php"); ?>
    <style>
        /* تعریف فونت سمیم */
@font-face {
    font-family: 'Samim';
    src: url('assets/fonts/Samim.eot');
    src: url('assets/fonts/Samim.eot?#iefix') format('embedded-opentype'),
         url('assets/fonts/Samim.woff2') format('woff2'),
         url('assets/fonts/Samim.woff') format('woff'),
         url('assets/fonts/Samim.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

body {
    background-color: white;
    font-family: 'Samim', sans-serif;
}
.main-carousel {
    width: 100%;
    position: relative;
    margin-top: 0; /* چسبیدن به هدر */
    margin-bottom: 2rem;
}

.carousel {
    position: relative;
    width: 100%;
    max-width: 1200px; /* عرض ثابت حداکثر */
    height: 400px; /* ارتفاع ثابت */
    margin: 0 auto; /* وسط چین کردن */
    overflow: hidden;
}

.carousel-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    display: none;
    /* حذف انیمیشن اسلاید */
    transition: none !important;
    animation: none !important;
}

.img-optimize {
    width: 100%;
    height: 100%;
    object-fit: cover; /* تغییر از contain به cover برای پر کردن کامل کادر */
    transition: transform 0.3s ease;
    backface-visibility: hidden;
    transform-origin: center;
    margin: auto;
}

/* کنترل‌های اسلایدر */
.carousel-controls, .carousel-indicators {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 5px;
    z-index: 10;
    padding: 6px 12px;
    border-radius: 20px;
}

.carousel-dot {
    width: 12px;
    height: 6px;
    border-radius: 3px;
    background-color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
    margin: 0 3px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.carousel-dot::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.carousel-dot:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: scaleX(1.2);
}

.carousel-dot.active {
    width: 24px;
    background-color: #3b82f6;
    transform: scaleY(1.1);
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
}

.carousel-dot.active::after {
    animation: shine 2s infinite;
}

.carousel-arrows {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
    z-index: 5;
}

.carousel-arrow {
    width: 45px;
    height: 45px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #3b82f6;
    margin: 0 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
    /* اطمینان از دایره کامل بودن */
    overflow: hidden;
    position: relative !important;
}

/* افکت shine برای دکمه‌های اسلاید عکس‌ها در حالت کلیک */
.carousel-arrow::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
}

/* انیمیشن ظاهر شدن دکمه‌ها با هاور */
.carousel:hover .carousel-arrow {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.carousel-arrow:hover {
    background-color: rgba(255, 255, 255, 0.7);
}

.carousel-arrow:active {
    animation: button-press 0.3s ease;
}

.carousel-arrow:active::after {
    animation: shine 1s;
}

/* حذف استایل اکتیو برای جلوگیری از تغییر دکمه هنگام کلیک */



/* استایل‌های مخصوص موبایل */
@media (max-width: 768px) {
    .carousel {
        height: 200px; /* ارتفاع کمتر در موبایل */
        margin-top: 0;
    }

    .carousel-arrow {
        width: 30px;
        height: 30px;
        margin: 0 5px;
    }

    .carousel-controls, .carousel-indicators {
        bottom: 10px;
    }

    .carousel-dot {
        width: 10px;
        height: 4px;
    }

    .carousel-dot.active {
        width: 20px;
    }

    /* افزایش سرعت اسلایدر در موبایل */
    .carousel-item {
        transition: opacity 0.3s ease-in-out;
    }
}
.container {
    max-width: 100%;
    overflow-x: hidden !important;
}

.slide-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-right: 1cm;
    padding-left: 1cm;
}

.slide-header h1 {
    font-size: 25px;
    font-family: 'Samim', sans-serif;
}

.product-card {
    height: 400px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.product-card figure {
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(67, 66, 66, 0.58);
}

.product-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
}

.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.price-container {
    border-top: 1px solid #eee;
    padding-top: 0.5rem;
}

.original-price {
    text-decoration: line-through;
    color: #dc2626;
}

.discount-price {
    color: #000000;
}

.product-card .original-price {
    text-decoration: line-through;
    color: #dc2626;
    margin-left: 10px;
}

.product-card .discount-price {
    color: #000000;
}

.product-card .unavailable {
    color: red;
    font-weight: bold;
    width: 100% !important;
}

.slide-container {
    width: 100%;
    margin-bottom: 3cm;
    position: relative;
    padding: 0 1cm;
    overflow: visible;
}

.slide-container .swiper {
    background-color: white;
    border-radius: 1rem;
    padding: 0.5rem;
    overflow: visible;
    margin: 0 -10px;
    padding: 10px;
}

@media (max-width: 768px) {
    .slide-container {
        padding: 0 0.5cm;
        margin-bottom: 2cm;
        overflow: visible;
    }

    .slide-container .swiper {
        margin: 0;
        padding: 5px;
        overflow: visible;
    }
}

.slide-title {
    text-align: right;
    width: 100%;
    font-family: 'Samim', sans-serif;
}

.swiper-slide {
    width: auto;
    height: auto;
    margin-right: 0.25cm !important;
    margin-left: 0.25cm !important;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

@media (max-width: 768px) {
    .swiper-slide {
        width: 80% !important;
        margin-right: 0.15cm !important;
        margin-left: 0.15cm !important;
        box-sizing: border-box;
    }

    .swiper-wrapper {
        display: flex;
        align-items: stretch;
        width: 100%;
    }

    .swiper {
        overflow: visible !important;
        width: 100%;
        padding: 0 !important;
    }
}

.swiper-wrapper {
    padding: 1rem 0;
    display: flex;
    align-items: stretch;
}

.product-link {
    text-decoration: none;
    color: inherit;
}

.custom-cart-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"/><circle cx="20" cy="21" r="1"/><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/></svg>');
    background-size: cover;
    vertical-align: middle;
    margin-left: 5px;
}

.swiper-button-next,
.swiper-button-prev {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    color: #3b82f6 !important;
    border-radius: 50%;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    opacity: 0.9;
    background-color: #f0f9ff;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
}

.swiper-button-next {
    right: 10px;
}

.swiper-button-prev {
    left: 10px;
}

/* استایل‌های دکمه بازگشت به بالا */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.9);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-5px) scale(1.05);
}

.scroll-to-top svg {
    margin: 0 auto;
    width: 24px;
    height: 24px;
    stroke-width: 3;
}

@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 80px;
        right: 20px;
        width: 50px;
        height: 50px;
    }

    .scroll-to-top svg {
        width: 20px;
        height: 20px;
    }
}

.animation-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0.5rem;
    border: 3px solid transparent;
    pointer-events: none;
    animation: border-animation 2s linear infinite;
}

@keyframes border-animation {
    0% {
        box-shadow: 0 0 0px #2563eb;
        border-color: transparent;
    }

    50% {
        box-shadow: 0 0 15px #2563eb;
        border-color: #2563eb;
    }

    100% {
        box-shadow: 0 0 0px #2563eb;
        border-color: transparent;
    }
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.card {
    transition: transform 0.5s, opacity 0.5s;
    transform: translateY(50px);
    opacity: 0;
}

.card:hover {
    transform: translateY(0);
    opacity: 1;
}

.card.fade-in {
    opacity: 1;
    transform: translateY(0);
}

.image-wrapper {
    transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
    transform: translateX(100%);
    opacity: 0;
}

.carousel {
    transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
    transform: translateX(-100%);
    opacity: 0;
}

.fade-in {
    transform: translateX(0);
    opacity: 1;
}

.carousel {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.carousel-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    display: none;
    transition: none !important;
    animation: none !important;
}

.carousel-item.active {
    opacity: 1;
    display: block;
    transition: none !important;
    animation: none !important;
}

.card-motion {
    transform: translateY(0);
    opacity: 1;
    transition: box-shadow 0.3s ease;
    position: relative;
    width: 100%;
    max-width: 320px; /* اندازه ثابت عرض */
    height: 400px; /* اندازه ثابت ارتفاع */
}

.card-motion:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* استایل دکمه روی کارت‌ها */
.card-button {
    padding: 0.8em 1.5em;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    letter-spacing: 0px;
    text-transform: uppercase;
    cursor: pointer;
    color: #3b82f6;
    transition: all 1000ms;
    font-size: 14px;
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    overflow: hidden;
    outline: 2px solid #3b82f6;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    font-family: 'Samim', sans-serif;
    direction: rtl;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease, transform 1000ms;
}

.card-motion:hover .card-button {
    opacity: 1;
}

.card-button:hover {
    color: #ffffff;
    transform: translateX(-50%) scale(1.1);
    outline: 2px solid #60a5fa;
    box-shadow: 4px 5px 17px -4px #2563eb;
}

.card-button::before {
    content: none;
}

@media (max-width: 768px) {
    .card-button {
        padding: 0.6em 1em;
        font-size: 12px;
        bottom: 10px;
    }
}

.img-optimize {
    width: 100%;
    height: 100%;
    object-fit: cover; /* تغییر از contain به cover برای پر کردن کامل کادر */
    transition: transform 0.3s ease;
    backface-visibility: hidden;
    transform-origin: center;
    margin: auto;
}

.image-wrapper,
.carousel-item,
.card-motion {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
}

.card-motion:hover .img-optimize,
.image-wrapper:hover .img-optimize {
    transform: scale(1.05);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-motion {
    animation: fadeIn 0.3s ease-out;
    transform-origin: center;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel {
    backface-visibility: hidden;
    perspective: 1000px;
}

.carousel-item {
    transition: opacity 0.5s ease-out;
    will-change: opacity;
}

/* استایل دکمه‌های جدید اسلایدر */
.contactButton {
    background: #e0f2fe; /* آبی کم‌رنگ */
    border: none;
    outline: none;
    position: relative;
    padding: 10px;
    color: #3b82f6;
    border-radius: 5px;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    border: 1px solid #3b82f6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    width: 44px;
    height: 45px;
    overflow: hidden; /* برای افکت موج */
    transition: all 0.3s ease;
}

.contactButton:hover {
    background: #93c5fd; /* آبی پررنگ‌تر برای hover */
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
    font-weight: bold;
    transform: scale(1.05);
}

.iconButton {
    display: flex;
    align-items: center;
    justify-content: center;
    /* حذف transition */
}

.iconButton svg {
    width: 24px;
    height: 24px;
    stroke-width: 2.5;
}

.contactButton .iconButton svg path {
    stroke-width: 2.5;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.contactButton.prev .iconButton svg {
    transform: rotate(180deg);
}

.button-click {
    animation: button-press 0.3s ease;
    background: #3b82f6 !important; /* آبی پررنگ‌تر برای کلیک */
    color: white !important;
    border: none !important;
    box-shadow: 0 3px 5px rgba(37, 99, 235, 0.3) !important;
    font-weight: bold !important;
}

@keyframes button-press {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.92);
    }
    100% {
        transform: scale(1);
    }
}

/* افکت موج (ripple effect) برای دکمه‌ها */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(37, 99, 235, 0.8); /* رنگ انیمیشن موج پررنگ‌تر */
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
    z-index: 10; /* اطمینان از نمایش بالاتر از سایر عناصر */
}

@keyframes ripple {
    to {
        transform: scale(2.5);
        opacity: 0;
    }
}

.slider-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* استایل دکمه همه محصولات */
.all-products-button {
    padding: 0.7em 1.5em;
    border: 1px solid #3b82f6;
    border-radius: 0.5rem;
    font-weight: bold;
    letter-spacing: 0px;
    text-transform: uppercase;
    cursor: pointer;
    color: #3b82f6;
    transition: all 0.3s ease;
    font-size: 14px;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    font-family: 'Samim', sans-serif;
    direction: rtl;
    background: #e0f2fe; /* آبی کم‌رنگ مثل دکمه‌های ناوبری */
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.all-products-button:hover {
    color: #ffffff;
    transform: scale(1.05) translateY(-1px);
    outline: none;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1);
    background: #93c5fd; /* آبی پررنگ‌تر برای hover مثل دکمه‌های ناوبری */
    border: none;
    transition: all 0.3s ease;
}

/* حذف افکت shine و استفاده از افکت موج مثل دکمه‌های ناوبری */
.all-products-button::after {
    content: none;
}

.all-products-button:active {
    transform: translateY(1px) scale(0.95);
    box-shadow: 0 1px 2px rgba(37, 99, 235, 0.2), 0 1px 1px rgba(0, 0, 0, 0.08);
    background: #3b82f6 !important; /* آبی پررنگ‌تر برای کلیک مثل دکمه‌های ناوبری */
    transition: all 0.3s ease;
}

.all-products-button::before {
    content: none;
}

/* استایل‌های جدید برای ریسپانسیو موبایل */
/* حالت موبایل */
@media (max-width: 768px) {

    /* مخفی کردن دکمه‌های اسلایدر در موبایل */
    .slider-controls {
        display: none !important;
    }

    /* تنظیم استایل دکمه‌های swiper در موبایل */
    .swiper-button-next,
    .swiper-button-prev {
        width: 30px;
        height: 30px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 14px;
    }

    /* تنظیم استایل دکمه همه محصولات در موبایل */
    .all-products-button {
        padding: 0.6em 1em;
        font-size: 12px;
        letter-spacing: 0px;
    }

    /* تنظیم padding در موبایل */
    .slide-container {
        padding: 0 0.5cm;
        margin-bottom: 1.5cm;
    }

    .slide-header {
        padding-right: 0.5cm;
        padding-left: 0.5cm;
    }

    /* تنظیم سایز عنوان */
    .slide-header h1 {
        font-size: 20px;
    }

    /* تنظیم چیدمان برای فضای محدود موبایل */
    .product-card {
        height: 350px;
        width: 100% !important;
        min-width: 0 !important;
        max-width: 100% !important;
    }

    .swiper-slide {
        display: flex;
        justify-content: center;
    }

    .product-link {
        width: 100%;
    }

    /* تنظیم سایز فونت */
    .card-title {
        font-size: 14px !important;
    }

    /* تنظیم برای کاروسل و تصاویر کناری */
    .main-section {
        flex-direction: column;
    }

    .carousel-col {
        width: 100% !important;
        margin-bottom: 1rem;
    }

    .side-images-col {
        width: 100% !important;
        flex-direction: row !important;
    }

    .side-images-col .image-wrapper {
        width: 48% !important;
        height: 150px !important;
    }
}

/* Main section with responsive layout */
.main-section {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Right Column: Main Carousel */
.carousel-col {
    width: 66.666667%;
    order: 2; /* تغییر ترتیب در دسکتاپ - دوم */
}

/* Left Column: Side Images */
.side-images-col {
    width: 33.333333%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    order: 1; /* تغییر ترتیب در دسکتاپ - اول */
}

.carousel {
    width: 100%;
    height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.image-wrapper {
    height: 195px;
    border-radius: 0.5rem;
    overflow: hidden;
}

/* تنظیمات ریسپانسیو برای موبایل */
@media (max-width: 768px) {
    .main-section {
        flex-direction: column;
    }

    .carousel-col {
        width: 100%;
        order: 1; /* در موبایل اسلایدر اول باشد */
        margin-bottom: 1rem;
    }

    .side-images-col {
        width: 100%;
        flex-direction: row;
        order: 2; /* در موبایل تصاویر کناری دوم باشند */
    }

    .carousel {
        height: 250px; /* ارتفاع کمتر در موبایل */
        margin-bottom: 1rem;
    }

    .image-wrapper {
        width: calc(50% - 0.5rem);
        height: 150px;
    }

    .container {
        padding: 0.5rem;
    }
}

/* تنظیمات برای صفحه‌های خیلی کوچک */
@media (max-width: 480px) {
    .carousel {
        height: 200px;
    }

    .image-wrapper {
        height: 120px;
    }
}

/* استایل‌های جدید برای دکمه‌های کاروسل */
.btn-circle {
    width: 45px !important;
    height: 45px !important;
    border-radius: 50% !important;
    background-color: rgba(255, 255, 255, 0.8) !important;
    color: #3b82f6 !important;
    font-size: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    border: 2px solid transparent !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    z-index: 20 !important;
    position: absolute !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    /* اطمینان از دایره کامل بودن */
    overflow: hidden !important;
    /* حذف انیمیشن بزرگ شدن دکمه هنگام کلیک */
    -webkit-tap-highlight-color: transparent !important;
}

/* افکت shine برای دکمه‌های اسلاید عکس‌ها در حالت کلیک */
.btn-circle::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
}

/* انیمیشن ظاهر شدن دکمه‌ها با هاور */
.carousel:hover .btn-circle {
    opacity: 1 !important;
    transition: opacity 0.3s ease !important;
}

.btn-circle:hover {
    background-color: rgba(255, 255, 255, 0.8) !important;
    color: #3b82f6 !important;
}

.btn-circle:active {
    animation: button-press 0.3s ease;
}

.btn-circle:active::after {
    animation: shine 1s;
}



/* حذف استایل اکتیو برای جلوگیری از تغییر دکمه هنگام کلیک */

/* اطمینان از نمایش صحیح دکمه‌ها در حالت دسکتاپ و مخفی بودن در موبایل */
@media (min-width: 768px) {
    .btn-circle {
        display: flex !important;
    }
}

@media (max-width: 767px) {
    /* دکمه‌های اسلاید عکس‌ها در موبایل نمایش داده نمی‌شوند */
    .btn-circle {
        display: none !important;
    }
}

/* استایل دکمه افزودن به سبد خرید */
.add-to-cart {
    width: 100%;
    height: 40px;
    border-radius: 12px;
    border: none;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.103);
    position: relative;
    font-family: 'Samim', sans-serif !important;
    direction: rtl !important;
    gap: 4px; /* فاصله کم بین عناصر داخلی */
}

.add-to-cart .IconContainer {
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-to-cart .cart-icon {
    fill: white;
    height: 1.2em;
    width: 1.2em;
}

.add-to-cart .text {
    height: 100%;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white !important;
    font-size: 12px !important;
    font-weight: 600;
    margin: 0;
    padding: 0;
}

.add-to-cart:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.add-to-cart:active {
    background: linear-gradient(135deg, #1d4ed8 0%, #172554 100%) !important;
}

@media (max-width: 768px) {
    .add-to-cart {
        height: 35px;
        font-size: 11px !important;
    }

    .add-to-cart .text {
        font-size: 11px !important;
    }
}

/* استایل برای اسم محصول */
.product-name {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    direction: rtl;
    font-family: 'Samim', sans-serif;
}

/* استایل برای اسم محصول انگلیسی */
.product-name-en {
    direction: ltr !important;
    text-align: left !important;
    font-family: 'Samim', sans-serif;
}

/* استایل‌های عمومی متنی */
h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea, select, option {
    font-family: 'Samim', sans-serif;
}

/* حذف هرگونه تغییر دکمه هنگام کلیک اما حفظ انیمیشن ظاهر شدن */
button:active, .btn:active, .btn-circle:active, .carousel-arrow:active,
button:focus, .btn:focus, .btn-circle:focus, .carousel-arrow:focus {
    scale: 1 !important;
    box-shadow: none !important;
    outline: none !important;
}

/* استایل خاص برای تایتل‌ها */
.slide-header h1 {
    font-size: 25px;
    font-family: 'Samim', sans-serif;
}

.slide-title {
    text-align: right;
    width: 100%;
    font-family: 'Samim', sans-serif;
}

/* سایر متن‌ها */
.swiper-slide, .card-body, .card-title, .price-container, .discount-price, .original-price, .search-no-results {
    font-family: 'Samim', sans-serif;
}

/* استایل برای نمایش گزینه انتخاب شده در دراپ‌داون با رنگ آبی */
.dropdown-content .selected-option {
    background-color: #3b82f6 !important;
    color: white !important;
}

/* استایل برای هاور روی گزینه‌های دراپ‌داون */
.dropdown-content .option:hover {
    background-color: #dbeafe !important;
    color: #1e40af !important;
}

@media (max-width: 768px) {
    .side-images-col {
        width: 100% !important;
        flex-direction: column !important;
        gap: 0.7rem !important;
        order: 2;
        margin-top: -0.5rem !important;
    }
    .side-images-col .image-wrapper {
        width: 100% !important;
        height: 90px !important;
        margin-bottom: 0 !important;
        border-radius: 14px !important;
    }
    .side-images-col .image-wrapper .animation-border {
        border-radius: 14px !important;
    }
}

@media (max-width: 768px) {
    /* چهار کارد پایین به صورت grid دو ستونه */
    .container.mx-auto.flex.flex-wrap.justify-center.gap-4.md\:gap-6.px-4.md\:px-8.py-8.md\:py-12 {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 8px !important;
        padding: 0.3rem !important;
    }
    .card-motion {
        height: 175px !important;
        width: 100% !important;
        min-width: 0 !important;
        max-width: 100% !important;
        border-radius: 16px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.07);
        background: #fff !important;
        margin: 0 !important;
        transition: box-shadow 0.3s, transform 0.3s;
    }
}
/* اضافه کردن این استایل‌ها به بخش <style> صفحه */
body.loading .card-motion {
    visibility: hidden;
}

.card-motion {
    width: 100%;
    max-width: 320px;
    height: 400px;
    margin: 0;
    padding: 0;
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    transform: none !important;
    transition: none !important;
    animation: none !important;
}

@media (max-width: 768px) {
    .card-motion {
        max-width: 100%;
        height: 175px;
    }
}

/* تنظیم container اصلی */
.container.mx-auto.flex.flex-wrap.justify-center {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: stretch !important;
    gap: 1rem !important;
}
    </style>
</head>
<br><br>

<body class="bg-white">
    <?php if (isset($_GET['welcome']) && $_GET['welcome'] == 'true' && isset($_GET['name'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            Swal.fire({
                title: 'ورود موفقیت‌آمیز',
                text: '<?php echo htmlspecialchars(urldecode($_GET['name'])); ?> عزیز، به حساب کاربری خود خوش آمدید!',
                icon: 'success',
                toast: true,
                position: 'top',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true
            });
        });
    </script>
    <?php endif; ?>

    <!-- Container for main carousel and side images with responsive layout -->
    <div class="container mx-auto px-4 py-8">
        <!-- Main section with responsive layout -->
        <div class="main-section flex flex-col md:flex-row gap-4">
            <!-- Right Column: Main Carousel (در موبایل اول نمایش داده می‌شود) -->
            <div class="carousel-col w-full md:w-2/3 mb-4 md:mb-0">
                <div class="carousel w-full aspect-[16/9] min-h-[300px] bg-gray-100 overflow-hidden relative rounded-lg">
                    <!-- Slide 1 -->
                    <div id="slide1" class="carousel-item w-full h-full">
                        <img src="image/1.jpg" alt="Slide 1" width="800" height="450"
     class="img-optimize absolute inset-0 w-full h-full object-contain">

                    </div>
                    <!-- Slide 2 -->
                    <div id="slide2" class="carousel-item w-full h-full">
                        <img src="image/2.jpg" alt="Slide 2" width="800" height="450"
     class="img-optimize absolute inset-0 w-full h-full object-contain">

                    </div>
                    <!-- Slide 3 -->
                    <div id="slide3" class="carousel-item w-full h-full">
                        <img src="image/3.jpg" alt="Slide 3" width="800" height="450"
     class="img-optimize absolute inset-0 w-full h-full object-contain">

                    </div>

                    <!-- Navigation Buttons (مخفی در موبایل) -->
                    <div id="prev"
                        class="btn-circle absolute top-1/2 left-2 z-10 hidden md:flex items-center justify-center" style="margin-top: -20px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </div>
                    <div id="next"
                        class="btn-circle absolute top-1/2 right-2 z-10 hidden md:flex items-center justify-center" style="margin-top: -20px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </div>

                    <!-- Carousel Indicators -->
                    <div class="carousel-indicators absolute bottom-3 left-0 right-0 flex justify-center gap-2 z-10">
                        <div class="carousel-dot" data-index="0"></div>
                        <div class="carousel-dot" data-index="1"></div>
                        <div class="carousel-dot" data-index="2"></div>
                    </div>
                </div>
            </div>

            <!-- Left Column: Side Images (در موبایل بعد از کاروسل نمایش داده می‌شود) -->
            <div class="side-images-col flex flex-col md:w-1/3 gap-4">
                <!-- Top Image -->
                <a href="/shop" style="display:block;">
                    <div class="image-wrapper h-32 md:h-48 bg-gray-100 rounded-lg overflow-hidden relative">
                        <img src="image/tabliq4.webp" alt="Top Image"
                            class="img-optimize absolute inset-0 w-full h-full object-contain">
                        <div class="animation-border"></div>
                    </div>
                </a>
                <!-- Bottom Image -->
                <div class="image-wrapper h-32 md:h-48 bg-gray-100 rounded-lg overflow-hidden relative">
                    <img src="image/tabliq5.webp" alt="Bottom Image"
                        class="img-optimize absolute inset-0 w-full h-full object-contain">
                    <div class="animation-border"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Cards Container - Responsive -->
    <div class="container mx-auto flex flex-wrap justify-center gap-4 md:gap-6 px-4 md:px-8 py-8 md:py-12">
        <!-- Card 1 -->
        <a href="/shop?category=<?php echo urlencode($card_categories[1]); ?>"
            class="card-motion rounded-lg overflow-hidden w-full sm:w-2/5 md:w-80 h-64 md:h-96 relative bg-gray-100">
            <img src="image/tabliq6.webp" alt="Card 1"
                class="img-optimize absolute inset-0 w-full h-full object-contain">
        </a>

        <!-- Card 2 -->
        <a href="/shop?category=<?php echo urlencode($card_categories[2]); ?>"
            class="card-motion rounded-lg overflow-hidden w-full sm:w-2/5 md:w-80 h-64 md:h-96 relative bg-gray-100">
            <img src="image/tabliq7.webp" alt="Card 2"
                class="img-optimize absolute inset-0 w-full h-full object-contain">
        </a>

        <!-- Card 3 -->
        <a href="/shop?category=<?php echo urlencode($card_categories[3]); ?>"
            class="card-motion rounded-lg overflow-hidden w-full sm:w-2/5 md:w-80 h-64 md:h-96 relative bg-gray-100">
            <img src="image/tabliq8.webp" alt="Card 3"
                class="img-optimize absolute inset-0 w-full h-full object-contain">
        </a>

        <!-- Card 4 -->
        <a href="/shop?category=<?php echo urlencode($card_categories[4]); ?>"
            class="card-motion rounded-lg overflow-hidden w-full sm:w-2/5 md:w-80 h-64 md:h-96 relative bg-gray-100">
            <img src="image/tabliq9.webp" alt="Card 4"
                class="img-optimize absolute inset-0 w-full h-full object-contain">
        </a>
    </div>

    <br><br><br><br>
    <div class="container mx-auto">
        <?php foreach ($slide_details as $slide => $details): ?>
            <div class="slide-container">
                <div class="slide-header">
                    <div class="text-right">
                        <h1 class="font-bold text-black mb-2"><?php echo htmlspecialchars($details['title']); ?></h1>
                    </div>
                    <div class="flex items-center gap-2">
                        <!-- دکمه‌ها در موبایل مخفی می‌شوند با استایل slider-controls -->
                        <div class="slider-controls">
                            <button class="swiper-button-prev-<?php echo $slide; ?> contactButton next">
                                <div class="iconButton">
                                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                                        fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M5 12h14"></path>
                                        <path d="M13 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </button>
                            <button class="swiper-button-next-<?php echo $slide; ?> contactButton prev">
                                <div class="iconButton">
                                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                                        fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M5 12h14"></path>
                                        <path d="M13 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </button>
                        </div>
                        <a href="/shop?category=<?php echo urlencode($details['category']); ?>"
                            class="all-products-button">همه محصولات</a>
                    </div>
                </div>
                <!-- اسلایدر محصولات با تنظیمات ریسپانسیو -->
                <div class="w-full">
                    <div class="swiper slide-<?php echo $slide; ?>">
                        <div class="swiper-wrapper">
                            <?php
                            // کوئری برای دریافت 15 محصول آخر از هر دسته‌بندی
                            $category = $details['category'];
                            $product_query = "SELECT p.*,
                            (SELECT image FROM product_images WHERE product_id = p.id LIMIT 1) as first_image
                            FROM products p
                            WHERE p.category = '$category'
                            ORDER BY p.created_at DESC
                            LIMIT 15";

                            $product_result = $conn->query($product_query);

                                if ($product_result && $product_result->num_rows > 0):
                                while ($product = $product_result->fetch_assoc()):
                                    // محاسبه قیمت نهایی با منطق جدید
                                    $price_aed = $product['product_price_aed'] ?? null;
                                    $discount_aed = $product['discount_price_aed'] ?? null;
                                    $price_toman = $product['product_price'] ?? 0;
                                    $discount_toman = $product['discount_price'] ?? 0;

                                    $final_price = 0;
                                    $old_price = 0;
                                    $has_discount = false;

                                    if ($price_aed !== null && $price_aed > 0) {
                                        $final_price = $price_aed * $aed_to_toman_rate;
                                        if ($discount_aed !== null && $discount_aed > 0) {
                                            $old_price = $final_price;
                                            $final_price = $discount_aed * $aed_to_toman_rate;
                                            $has_discount = true;
                                        }
                                    } elseif ($price_toman > 0) {
                                        $final_price = $price_toman;
                                        if ($discount_toman > 0) {
                                            $old_price = $final_price;
                                            $final_price = $discount_toman;
                                            $has_discount = true;
                                        }
                                    }
                                    ?>
                                    <div class="swiper-slide">
                                        <a href="/card?id=<?php echo $product['id']; ?>" class="product-link">
                                            <div class="card w-full bg-base-100 shadow-xl product-card">
                                                <figure class="relative h-40 md:h-48 overflow-hidden">
                                                    <?php if (!empty($product['first_image'])): ?>
                                                        <img src="data:image/jpeg;base64,<?php echo base64_encode($product['first_image']); ?>"
                                                            alt="<?php echo htmlspecialchars($product['product_name']); ?>"
                                                            class="w-full h-full object-contain hover:scale-105 transition-transform duration-300" />
                                                    <?php endif; ?>
                                                </figure>
                                                <div class="card-body">
                                                    <h2 class="card-title text-xs sm:text-sm line-clamp-1 text-gray-900">
                                                        <div class="product-content">
                                                            <h3 class="product-name <?= ctype_alpha($product['product_name'][0]) ? 'product-name-en' : '' ?>"><?= htmlspecialchars($product['product_name']) ?></h3>
                                                        </div>
                                                    </h2>
                                                    <p class="text-xs line-clamp-2 text-gray-500">
                                                        <?php echo htmlspecialchars($product['product_description']); ?></p>
                                                    <div class="card-actions flex-col justify-end mt-auto">
                                                        <?php if ($product['stock_quantity'] > 0): ?>
                                                            <div class="price-container flex flex-col items-start w-full">
                                                                <?php if ($has_discount): ?>
                                                                    <span class="original-price text-xs"><?php echo number_format($old_price); ?> تومان</span>
                                                                    <span class="discount-price text-sm font-bold"><?php echo number_format($final_price); ?> تومان</span>
                                                                <?php else: ?>
                                                                    <span class="text-sm font-bold text-black"><?php echo number_format($final_price); ?> تومان</span>
                                                                <?php endif; ?>
                                                                <span class="text-xs text-red-600 mt-1">موجودی: <?php echo $product['stock_quantity']; ?></span>
                                                            </div>
                                                            <button class="btn btn-primary btn-sm w-full mt-2 add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                                                <span class="text">افزودن به سبد خرید</span><span class="IconContainer"><svg class="cart-icon" viewBox="0 0 576 512" height="1em" xmlns="http://www.w3.org/2000/svg"><path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path></svg></span>
                                                            </button>
                                                        <?php else: ?>
                                                            <span
                                                                class="w-full py-2 px-4 bg-red-50 text-red-600 font-medium text-center rounded-lg border border-red-200">ناموجود</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                <?php
                                endwhile;
                            endif;
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Scroll to Top Button -->
    <div class="scroll-to-top" onclick="scrollToTop()">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
            class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M5 15l7-7 7 7" />
        </svg>
        <span class="mt-1 text-sm"></span>
    </div>

    <script src="assets/js/swiper-bundle.min.js"></script>
    <script>
        // کد اصلی کاروسل - بهینه‌سازی شده و رفع باگ دکمه‌ها
        document.addEventListener('DOMContentLoaded', function() {
            // متغیرهای اصلی
            const prevBtn = document.getElementById('prev');
            const nextBtn = document.getElementById('next');
            const slides = document.querySelectorAll('.carousel-item');
            const dots = document.querySelectorAll('.carousel-dot');

            // بررسی وجود اسلایدها
            if (!slides.length) return;

            let currentIndex = 0;
            let isTransitioning = false;
            let autoSlideInterval;

            // نمایش اسلاید اول
            showSlide(0);
            startAutoSlide();

            // ایجاد سیستم رویداد سفارشی برای اطمینان از عملکرد دکمه‌ها
            const carouselEvents = new EventTarget();

            // تنظیم رویدادهای کلیک دکمه‌ها با سیستم جدید
            if (prevBtn) {
                prevBtn.addEventListener('click', function() {
                    // ارسال رویداد سفارشی
                    const prevEvent = new CustomEvent('carousel:prev');
                    carouselEvents.dispatchEvent(prevEvent);
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', function() {
                    // ارسال رویداد سفارشی
                    const nextEvent = new CustomEvent('carousel:next');
                    carouselEvents.dispatchEvent(nextEvent);
                });
            }

            // اضافه کردن رویداد کلیک به نشانگرها
            dots.forEach((dot, index) => {
                dot.addEventListener('click', function() {
                    showSlide(index);
                    restartAutoSlide();
                });
            });

            // گوش دادن به رویدادهای سفارشی
            carouselEvents.addEventListener('carousel:next', function() {
                nextSlide();
            });

            carouselEvents.addEventListener('carousel:prev', function() {
                prevSlide();
            });

            // تنظیم رویدادهای کلید
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowRight') {
                    const nextEvent = new CustomEvent('carousel:next');
                    carouselEvents.dispatchEvent(nextEvent);
                } else if (e.key === 'ArrowLeft') {
                    const prevEvent = new CustomEvent('carousel:prev');
                    carouselEvents.dispatchEvent(prevEvent);
                }
            });

            // تنظیم رویدادهای لمسی
            const carousel = document.querySelector('.carousel');
            if (carousel) {
                let touchStartX = 0;
                let touchStartY = 0;

                carousel.addEventListener('touchstart', function(e) {
                    touchStartX = e.changedTouches[0].screenX;
                    touchStartY = e.changedTouches[0].screenY;
                    pauseAutoSlide();
                });

                carousel.addEventListener('touchend', function(e) {
                    const touchEndX = e.changedTouches[0].screenX;
                    const touchEndY = e.changedTouches[0].screenY;
                    const threshold = 50;

                    const deltaX = touchEndX - touchStartX;
                    const deltaY = touchEndY - touchStartY;

                    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
                        if (deltaX < 0) {
                            const nextEvent = new CustomEvent('carousel:next');
                            carouselEvents.dispatchEvent(nextEvent);
                        } else {
                            const prevEvent = new CustomEvent('carousel:prev');
                            carouselEvents.dispatchEvent(prevEvent);
                        }
                    }

                    startAutoSlide();
                });

                // توقف اسلاید خودار با هاور موس
                carousel.addEventListener('mouseenter', pauseAutoSlide);
                carousel.addEventListener('mouseleave', startAutoSlide);

                // اضافه کردن کلاس برای نشان دادن قابلیت سوایپ
                carousel.classList.add('swipeable');
            }

            // توابع اصلی
            function showSlide(index) {
                // مخفی کردن همه اسلایدها
                slides.forEach(slide => {
                    slide.style.transition = 'none';
                    slide.style.opacity = '0';
                    slide.style.display = 'none';
                    slide.classList.remove('active');
                });

                // نمایش اسلاید فعلی بدون انیمیشن
                slides[index].style.transition = 'none';
                slides[index].style.display = 'block';
                slides[index].style.opacity = '1';
                slides[index].classList.add('active');

                // به‌روزرسانی نشانگرها
                dots.forEach((dot, i) => {
                    if (i === index) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });

                currentIndex = index;
            }

            function nextSlide() {
                const newIndex = (currentIndex + 1) % slides.length;
                showSlide(newIndex);
                restartAutoSlide();
            }

            function prevSlide() {
                const newIndex = (currentIndex - 1 + slides.length) % slides.length;
                showSlide(newIndex);
                restartAutoSlide();
            }

            function startAutoSlide() {
                if (autoSlideInterval) clearInterval(autoSlideInterval);
                autoSlideInterval = setInterval(() => {
                    const nextEvent = new CustomEvent('carousel:next');
                    carouselEvents.dispatchEvent(nextEvent);
                }, 3000);
            }

            function pauseAutoSlide() {
                if (autoSlideInterval) {
                    clearInterval(autoSlideInterval);
                    autoSlideInterval = null;
                }
            }

            function restartAutoSlide() {
                pauseAutoSlide();
                startAutoSlide();
            }

            // اضافه کردن به متغیر جهانی برای دسترسی از بیرون
            window.carouselController = {
                next: () => {
                    const nextEvent = new CustomEvent('carousel:next');
                    carouselEvents.dispatchEvent(nextEvent);
                },
                prev: () => {
                    const prevEvent = new CustomEvent('carousel:prev');
                    carouselEvents.dispatchEvent(prevEvent);
                },
                show: showSlide,
                getCurrentIndex: () => currentIndex
            };

            // اضافه کردن به window برای دسترسی آسان
            window.carousel = window.carouselController;
        });

        // تنظیمات بهبود یافته Swiper
        <?php foreach ($slide_details as $slide => $details): ?>
            var swiper<?php echo $slide; ?> = new Swiper('.slide-<?php echo $slide; ?>', {
                slidesPerView: 1.2,
                spaceBetween: 8,
                speed: 600,
                grabCursor: true,
                loop: false,
                loopFillGroupWithBlank: false,
                centeredSlides: false,
                freeMode: false,
                slidesOffsetBefore: 0,
                slidesOffsetAfter: 0,
                allowTouchMove: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                keyboard: {
                    enabled: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next-<?php echo $slide; ?>',
                    prevEl: '.swiper-button-prev-<?php echo $slide; ?>',
                },
                breakpoints: {
                    320: {
                        slidesPerView: 1.1,
                        spaceBetween: 8,
                        slidesOffsetBefore: 10,
                        slidesOffsetAfter: 10,
                    },
                    480: {
                        slidesPerView: 1.5,
                        spaceBetween: 10,
                        slidesOffsetBefore: 10,
                        slidesOffsetAfter: 10,
                    },
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 15,
                    },
                    768: {
                        slidesPerView: 3,
                        spaceBetween: 15,
                    },
                    1024: {
                        slidesPerView: 4,
                        spaceBetween: 20,
                    },
                    1280: {
                        slidesPerView: 5,
                        spaceBetween: 20,
                    }
                },
                on: {
                    init: function() {
                        // اطمینان از نمایش صحیح اسلایدر در ابتدای بارگذاری
                        setTimeout(() => {
                            this.update();
                        }, 100);
                    },
                    slideChangeTransitionStart: function () {
                        // افکت فید برای اسلاید‌های جدید
                        const slides = this.slides;
                        for (let i = 0; i < slides.length; i++) {
                            slides[i].style.transition = 'opacity 0.5s ease';
                            if (i >= this.activeIndex && i < this.activeIndex + this.params.slidesPerView) {
                                slides[i].style.opacity = '1';
                            }
                        }
                    }
                }
            });
        <?php endforeach; ?>

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        document.addEventListener("DOMContentLoaded", () => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add("fade-in");
                    }
                });
            });

            const cards = document.querySelectorAll(".card");
            cards.forEach((card) => observer.observe(card));

            // بهبود عملکرد اسلایدرها با به‌روزرسانی آن‌ها پس از بارگذاری کامل صفحه
            setTimeout(() => {
                <?php foreach ($slide_details as $slide => $details): ?>
                    if (typeof swiper<?php echo $slide; ?> !== 'undefined') {
                        swiper<?php echo $slide; ?>.update();
                    }
                <?php endforeach; ?>
            }, 500);

            // بررسی حالت موبایل و به‌روزرسانی اسلایدرها برای اطمینان از نمایش صحیح
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                // به‌روزرسانی مجدد اسلایدرها برای حالت موبایل
                setTimeout(() => {
                    <?php foreach ($slide_details as $slide => $details): ?>
                        if (typeof swiper<?php echo $slide; ?> !== 'undefined') {
                            // تنظیم مجدد اسلایدر برای حالت موبایل
                            swiper<?php echo $slide; ?>.params.slidesPerView = 1.1;
                            swiper<?php echo $slide; ?>.params.spaceBetween = 8;
                            swiper<?php echo $slide; ?>.params.slidesOffsetBefore = 10;
                            swiper<?php echo $slide; ?>.params.slidesOffsetAfter = 10;
                            swiper<?php echo $slide; ?>.update();
                        }
                    <?php endforeach; ?>
                }, 1000);

                // اضافه کردن رویداد resize برای به‌روزرسانی اسلایدرها هنگام تغییر اندازه صفحه
                window.addEventListener('resize', function() {
                    <?php foreach ($slide_details as $slide => $details): ?>
                        if (typeof swiper<?php echo $slide; ?> !== 'undefined') {
                            swiper<?php echo $slide; ?>.update();
                        }
                    <?php endforeach; ?>
                });
            }
        });

        document.addEventListener("DOMContentLoaded", () => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add("fade-in");
                    }
                });
            });
            const images = document.querySelectorAll(".image-wrapper, .carousel");
            images.forEach((image) => observer.observe(image));
        });

        document.addEventListener("DOMContentLoaded", () => {
            // تنظیم اندازه‌های ثابت برای کارت‌های بزرگ قبل از نمایش
            const cardMotions = document.querySelectorAll(".card-motion");
            cardMotions.forEach((card) => {
                // اطمینان از اینکه کارت‌ها از ابتدا با opacity: 1 نمایش داده می‌شوند
                card.style.opacity = "1";
                card.style.transform = "translateY(0)";
                // حذف هرگونه انیمیشن اضافی
                card.style.animation = "none";
            });
            
            // کد مربوط به کارت‌های محصول بدون تغییر
            const productCards = document.querySelectorAll(".product-card");
            if (productCards.length > 0) {
                const productCardObserver = new IntersectionObserver((entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                entry.target.style.opacity = "1";
                                entry.target.style.transform = "translateY(0)";
                            }, 10);
                        }
                    });
                }, {
                    threshold: 0.1
                });

                productCards.forEach((card) => {
                    card.style.opacity = "0";
                    card.style.transform = "translateY(20px)";
                    card.style.transition = "opacity 0.3s ease, transform 0.3s ease";
                    productCardObserver.observe(card);
                });
            }
        });

        // انیمیشن کلیک دکمه‌های اسلایدر محصولات
        document.addEventListener("DOMContentLoaded", function () {
            // انتخاب همه دکمه‌های اسلایدر محصولات و دکمه همه محصولات
            const sliderButtons = document.querySelectorAll('.contactButton, .all-products-button');

            // اضافه کردن رویداد کلیک به هر دکمه
            sliderButtons.forEach(button => {
                button.addEventListener('click', function (event) {
                    // اضافه کردن کلاس انیمیشن
                    this.classList.add('button-click');

                    // حذف کلاس بعد از اتمام انیمیشن
                    setTimeout(() => {
                        this.classList.remove('button-click');
                    }, 300);

                    // افکت موج
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple-effect');

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);

                    ripple.style.width = ripple.style.height = `${size}px`;
                    ripple.style.left = `${event.clientX - rect.left - size/2}px`;
                    ripple.style.top = `${event.clientY - rect.top - size/2}px`;

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // انیمیشن موج آبی برای دکمه‌های ناوبری اسلاید عکس‌ها
            const carouselButtons = document.querySelectorAll('.btn-circle');

            carouselButtons.forEach(button => {
                button.addEventListener('click', function (event) {
                    // افکت موج
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple-effect');

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);

                    ripple.style.width = ripple.style.height = `${size}px`;
                    ripple.style.left = `${event.clientX - rect.left - size/2}px`;
                    ripple.style.top = `${event.clientY - rect.top - size/2}px`;

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // کد جدید برای دکمه scroll to top
        document.addEventListener('DOMContentLoaded', function() {
            const scrollToTopButton = document.querySelector('.scroll-to-top');

            // نمایش یا مخفی کردن دکمه بر اساس موقعیت اسکرول
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) { // نمایش دکمه بعد از 300 پیکسل اسکرول
                    scrollToTopButton.classList.add('show');
                } else {
                    scrollToTopButton.classList.remove('show');
                }
            });
        });

        // کد افزودن به سبد خرید
        document.addEventListener('DOMContentLoaded', function() {
            const addToCartButtons = document.querySelectorAll('.add-to-cart');

            addToCartButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation(); // جلوگیری از انتقال به صفحه محصول
                    const productId = this.getAttribute('data-product-id');

                    fetch('add_to_cart.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `product_id=${productId}&quantity=1`
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'موفقیت',
                                    text: data.message,
                                    confirmButtonText: 'باشه',
                                    confirmButtonColor: '#3085d6',
                                    customClass: {
                                        popup: 'swal-rtl'
                                    },
                                    preConfirm: () => {
                                        window.location.href = data.redirect;
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'خطا',
                                    text: data.message,
                                    confirmButtonText: 'باشه',
                                    confirmButtonColor: '#3085d6',
                                    customClass: {
                                        popup: 'swal-rtl'
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            Swal.fire({
                                icon: 'error',
                                title: 'خطا',
                                text: 'خطای غیرمنتظره‌ای رخ داد',
                                confirmButtonText: 'باشه',
                                confirmButtonColor: '#3085d6',
                                customClass: {
                                    popup: 'swal-rtl'
                                }
                            });
                        });
                });
            });

            // اعمال کلاس selected-option به گزینه انتخاب شده در دراپ‌داون
            const dropdowns = document.querySelectorAll('.dropdown');

            dropdowns.forEach(dropdown => {
                const options = dropdown.querySelectorAll('.option');
                const selectedValue = dropdown.querySelector('.selected-value');

                if (options && selectedValue) {
                    // ابتدا مقدار فعلی را پیدا کنیم
                    const currentValue = selectedValue.textContent.trim();

                    // اعمال کلاس به گزینه مطابق
                    options.forEach(option => {
                        if (option.textContent.trim() === currentValue) {
                            option.classList.add('selected-option');
                        } else {
                            option.classList.remove('selected-option');
                        }

                        // اضافه کردن رویداد کلیک برای انتخاب گزینه جدید
                        option.addEventListener('click', function() {
                            options.forEach(opt => opt.classList.remove('selected-option'));
                            this.classList.add('selected-option');
                        });
                    });
                }
            });
        });
    </script>
</body>
<?php include("footer.php"); ?>

</html>
<?php
$conn->close();
?>
