<?php
class StringValidator {
    private $jsonFilePath = 'strings.json';
    private $strings = [];

    public function __construct() {
        // اگر فایل JSON وجود داشته باشد، آن را بخوان
        if (file_exists($this->jsonFilePath)) {
            $jsonContent = file_get_contents($this->jsonFilePath);
            $this->strings = json_decode($jsonContent, true) ?: [];
        }
    }

    public function __destruct() {
        // ذخیره اطلاعات در فایل JSON هنگام حذف شیء
        if (!empty($this->strings)) {
            file_put_contents($this->jsonFilePath, json_encode($this->strings, JSON_PRETTY_PRINT));
        }
    }

    public function validateString($input) {
        // بررسی طول رشته (کمتر از 6 کاراکتر نباشد)
        if (strlen($input) < 6) {
            return "ورودی نامعتبر";
        }

        // بررسی وجود همزمان حروف و اعداد
        $hasLetter = preg_match('/[a-zA-Z]/', $input);
        $hasNumber = preg_match('/[0-9]/', $input);

        if (!($hasLetter && $hasNumber)) {
            return "ورودی نامعتبر";
        }

        // اگر رشته معتبر باشد، آن را با شناسه یکتا ذخیره کن
        $uniqueId = uniqid();
        $this->strings[$uniqueId] = $input;
        
        return "رشته با موفقیت ذخیره شد. شناسه: " . $uniqueId;
    }

    public function getAllStrings() {
        return $this->strings;
    }
}

// اگر فرم ارسال شده باشد
$message = "";
if (isset($_POST['submit'])) {
    $inputString = $_POST['input_string'] ?? '';
    
    $validator = new StringValidator();
    $message = $validator->validateString($inputString);
}

// نمایش رشته‌های ذخیره شده
$validator = new StringValidator();
$savedStrings = $validator->getAllStrings();
?>

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اعتبارسنجی رشته</title>
    <style>
        body {
            font-family: Tahoma, Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        input[type="submit"] {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .strings-list {
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>اعتبارسنجی رشته</h2>
        
        <form method="post" action="">
            <div class="form-group">
                <label for="input_string">رشته ورودی:</label>
                <input type="text" id="input_string" name="input_string" required>
            </div>
            <div class="form-group">
                <input type="submit" name="submit" value="بررسی و ذخیره">
            </div>
        </form>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $message === 'ورودی نامعتبر' ? 'error' : 'success'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($savedStrings)): ?>
            <div class="strings-list">
                <h3>رشته‌های ذخیره شده:</h3>
                <table>
                    <tr>
                        <th>شناسه</th>
                        <th>رشته</th>
                    </tr>
                    <?php foreach ($savedStrings as $id => $string): ?>
                        <tr>
                            <td><?php echo $id; ?></td>
                            <td><?php echo $string; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
