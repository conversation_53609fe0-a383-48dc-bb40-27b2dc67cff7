document.addEventListener('DOMContentLoaded', () => {
    const container = document.querySelector('.container');
    const form = document.querySelector('.registration-form');

    // Add a subtle tilt effect on mouse move
    container.addEventListener('mousemove', (e) => {
        const { left, top, width, height } = container.getBoundingClientRect();
        const x = (e.clientX - left) / width - 0.5;
        const y = (e.clientY - top) / height - 0.5;

        const rotateX = y * -10; // Invert y-axis for natural feel
        const rotateY = x * 10;

        container.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    });

    // Reset tilt when mouse leaves
    container.addEventListener('mouseleave', () => {
        container.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
        // Re-apply the floating animation
        container.style.animation = 'float 6s ease-in-out infinite';
    });
    
    // Stop floating animation during tilt
    container.addEventListener('mouseenter', () => {
        container.style.animation = 'none';
    });

    // Input fields animation
    const inputs = document.querySelectorAll('.input-group input');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            input.parentElement.classList.add('focused');
        });
        input.addEventListener('blur', () => {
            if (input.value === '') {
                input.parentElement.classList.remove('focused');
            }
        });
    });
});