/* تعریف فونت سمیم */
@font-face {
  font-family: 'Samim';
  src: url('../fonts/Samim.eot');
  src: url('../fonts/Samim.eot?#iefix') format('embedded-opentype'),
       url('../fonts/Samim.woff2') format('woff2'),
       url('../fonts/Samim.woff') format('woff'),
       url('../fonts/Samim.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* استایل کلی متن‌های فوتر */
footer, footer *, .footer-container, .footer-section, .footer-section h3, .footer-section p, .footer-section a, .footer-section ul li {
  font-family: 'Samim', sans-serif !important;
}

  footer.bg-white {
    box-shadow: 0px -6px 10px rgba(0, 0, 0, 0.1); /* سایه بالای فوتر */
  }

  .social-icons a {
    margin: 0 12px; /* فاصله مناسب بین لوگوها */
  }

  /* استایل‌های جدید برای فوتر ریسپانسیو */
  .footer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 0;
  }

  .footer-section {
    width: 100%;
    margin-bottom: 2rem;
    text-align: center;
  }

  /* اطمینان از عدم وجود فاصله اضافی */
  .logo-section {
    margin-right: 0;
    padding-right: 0;
  }

  .logo-section a, .logo-section img, .logo-section div {
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  .footer-sections-row {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* استایل‌های اختصاصی برای هر بخش */
  .logo-section {
    order: 1; /* در موبایل اول نمایش داده می‌شود */
  }

  .menu-section {
    order: 2; /* در موبایل دوم نمایش داده می‌شود */
  }

  .contact-social-section {
    order: 3; /* در موبایل سوم نمایش داده می‌شود */
  }

  @media (min-width: 768px) {
    .footer-sections-row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      padding: 0;
      margin: 0;
    }

    .footer-section {
      width: 30%;
      text-align: right;
    }

    /* در دسکتاپ لوگو سمت راست، منو وسط و اطلاعات تماس سمت چپ */
    .logo-section {
      order: 1; /* در دسکتاپ سمت راست */
      padding-right: 0;
      margin-right: 0;
    }

    .menu-section {
      order: 2; /* در دسکتاپ وسط */
    }

    .contact-social-section {
      order: 3; /* در دسکتاپ سمت چپ */
      padding-left: 0;
      margin-left: 0;
    }
  }
  .footer-section ul li a {
    position: relative;
    display: inline-block;
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #3b82f6;
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.footer-section ul li a:hover {
    color: #3b82f6;
}

.footer-section ul li a:hover::after {
    transform: scaleX(1);
}

.footer-section ul li a.active {
    color: #3b82f6;
}

.footer-section ul li a.active::after {
    transform: scaleX(1);
}

/* استایل‌های اضافی برای چیدمان جدید */
.logo-section img {
    margin-bottom: 2rem; /* افزایش فاصله بین لوگو و نماد اعتماد */
}

.logo-section {
    padding-right: 0; /* کاهش فاصله از سمت راست در حالت موبایل */
}

@media (min-width: 768px) {
    .logo-section {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        padding-right: 0; /* حذف فاصله از سمت راست در حالت دسکتاپ */
        text-align: right;
        margin-right: 0;
        width: 33%; /* تنظیم عرض دقیق */
    }

    .logo-section img {
        margin-right: 0; /* حذف فاصله از سمت راست لوگو */
    }

    .footer-sections-row {
        padding: 0; /* حذف پدینگ از کل ردیف فوتر */
        justify-content: flex-start; /* تغییر از space-between به flex-start */
        width: 100%;
    }

    .contact-social-section {
        text-align: right; /* راست چین کردن متن‌ها */
        padding-left: 0; /* حذف فاصله از سمت چپ */
        order: 3; /* در دسکتاپ سمت چپ */
        width: 33%; /* تنظیم عرض دقیق */
        margin-top: 2rem; /* اضافه کردن فاصله از بالا برای پایین آوردن سرمتن */
    }

    .contact-social-section h3 {
        text-align: right; /* راست چین کردن سرمتن‌ها */
    }

    .contact-social-section p {
        text-align: right; /* راست چین کردن پاراگراف‌ها */
    }

    .menu-section {
        text-align: center;
        order: 2; /* در دسکتاپ وسط */
        width: 33%; /* تنظیم عرض دقیق */
    }

    .menu-section ul {
        display: inline-block;
        text-align: right;
    }

    /* همه سرمتن‌ها در یک راستا */
    .footer-section h3 {
        text-align: right;
        margin-right: 0;
    }

    /* تنظیم دقیق موقعیت سرمتن‌ها */
    .footer-section h3 {
        margin-top: 0;
        padding-top: 0;
        margin-right: 0;
        padding-right: 0;
    }

    /* تنظیم فاصله یکسان برای همه سرمتن‌ها */
    .menu-section h3, .contact-social-section h3, .logo-section h3 {
        margin-bottom: 1.5rem;
    }
}