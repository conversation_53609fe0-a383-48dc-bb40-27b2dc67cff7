@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;700&display=swap');

body {
    font-family: 'Vazirmatn', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(45deg, #6a11cb, #2575fc);
    overflow: hidden;
    color: #fff;
}

.container {
    position: relative;
    width: 400px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
}

.registration-form h2 {
    text-align: center;
    margin-bottom: 30px;
    font-weight: 700;
    letter-spacing: 1px;
    color: #fff;
}

.input-group {
    position: relative;
    margin-bottom: 35px;
}

.input-group input {
    width: 100%;
    padding: 10px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(255, 255, 255, 0.5);
    outline: none;
    color: #fff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.input-group label {
    position: absolute;
    top: 10px;
    right: 0;
    color: rgba(255, 255, 255, 0.7);
    pointer-events: none;
    transition: all 0.3s ease;
}

.input-group input:focus ~ label,
.input-group input:valid ~ label {
    top: -15px;
    font-size: 12px;
    color: #2575fc;
}

.input-group input:focus,
.input-group input:valid {
    border-bottom-color: #fff;
}

button[type="submit"] {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(45deg, #ff007a, #a200ff);
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    letter-spacing: 1px;
}

button[type="submit"]:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #a200ff, #ff007a);
}

.login-link {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
}

.login-link a {
    color: #ff007a;
    text-decoration: none;
    font-weight: 700;
    transition: color 0.3s ease;
}

.login-link a:hover {
    color: #fff;
}