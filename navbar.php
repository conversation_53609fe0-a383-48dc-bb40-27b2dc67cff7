<?php
// Check if session is not already started before calling session_start()
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تعیین صفحه فعلی
$current_page = basename($_SERVER['PHP_SELF']);

if (isset($_SESSION['first_name']) && isset($_SESSION['last_name'])) {
    $first_name = $_SESSION['first_name'];
    $last_name = $_SESSION['last_name'];
}
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ناوبار</title>
    <link rel="icon" type="image/png" href="/image/icons8-apple-48.png" />
    <link href="assets/css/daisy.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="assets/webfonts/fontawsome.css?v=1.0">
    <link rel="stylesheet" href="assets/css/tailwind.css?v=1.0">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/navbar_style.css?v=1.4">
    <script src="assets/js/search.js"></script>
    <style>
    /* استایل ردیف جستجوهای اخیر */
    .recent-search-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 5px 0;
    }

    .search-item {
        display: flex;
        align-items: center;
        flex-grow: 1;
    }

    .search-item i {
        margin-left: 8px;
    }

    .remove-search {
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 14px;
        padding: 4px 8px;
        margin-right: 8px;
        transition: color 0.2s;
    }

    .remove-search:hover {
        color: #f44336;
    }

    /* اطمینان از عدم شکستن خط */
    .search-section li {
        margin-bottom: 5px;
    }

    .search-section ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    </style>
</head>

<body>
    <div class="top-black-bar">
        <div class="top-bar-right hide-on-mobile">
            <span>سیب موبایل</span><span> | فروشگاه محصولات اپل، کنسول های بازی و انواع گجت</span>
        </div>
        <div class="top-bar-center">
            <span>کد تخفیف ۱۰۰ هزار تومانی برای اولین خرید: </span><span class="discount-code">sibe</span>
        </div>
        <div class="top-bar-left hide-on-mobile">
            <span>۰۹۱۷۶۲۱۲۸۴۸</span>
            <i class="fas fa-phone-alt phone-icon"></i>
        </div>
    </div>
    <header class="header-container">
        <nav class="navbar">
        <!-- دکمه همبرگر منو -->
        <div class="hamburger-menu">
            <span class="bar"></span>
            <span class="bar"></span>
            <span class="bar"></span>
        </div>

        <!-- Overlay برای پس‌زمینه تیره -->
        <div class="overlay"></div>

        <!-- دکمه لوگو سمت چپ -->
            <div class="logo-container">
                <a href="/">
                    <img src="image/navbar_logo.png" alt="Logo">
                </a>
            </div>

        <!-- منوی موبایل -->
        <div class="mobile-menu">
            <div class="menu-section-header">
                <span>منوی اصلی</span>
            </div>
            
            <ul class="menu">
                <li><a href="/" class="<?php echo $current_page === 'index.php' ? 'active' : ''; ?>">خانه</a></li>
                <li><a href="/shop" class="<?php echo $current_page === 'shop.php' ? 'active' : ''; ?>">فروشگاه</a></li>
                <li><a href="/rules" class="<?php echo $current_page === 'rules.php' ? 'active' : ''; ?>">قوانین و مقررات</a></li>
                <li><a href="/contact_us" class="<?php echo $current_page === 'contact_us.php' ? 'active' : ''; ?>">تماس با ما</a></li>
            </ul>

            <div class="menu-divider"></div>
            
            <?php if(isset($_SESSION['user_id'])): ?>
                <div class="account-section-header">
                    <span>حساب کاربری</span>
                </div>
                
                <ul class="menu">
                    <li><a href="/profile" class="<?php echo $current_page === 'profile.php' ? 'active' : ''; ?>">پروفایل</a></li>
                    <li><a href="/orders" class="<?php echo $current_page === 'orders.php' ? 'active' : ''; ?>">سفارشات من</a></li>
                    <li><a href="/tickets" class="<?php echo $current_page === 'tickets.php' ? 'active' : ''; ?>">تیکت و پشتیبانی</a></li>
                    <li><a href="/addresses" class="<?php echo $current_page === 'addresses.php' ? 'active' : ''; ?>">آدرس‌های من</a></li>
                    <li><a href="/wallet" class="<?php echo $current_page === 'wallet.php' ? 'active' : ''; ?>">کیف پول</a></li>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                    <li><a href="/admin_panel" class="<?php echo $current_page === 'admin_panel.php' ? 'active' : ''; ?>">مدیریت سیستم</a></li>
                    <?php endif; ?>
                    <li><a href="/logout">خروج</a></li>
                </ul>
            <?php else: ?>
                <div class="account-section-header">
                    <span>ورود و ثبت نام</span>
                </div>
                
                <ul class="menu">
                    <li><a href="/login" class="<?php echo $current_page === 'login.php' ? 'active' : ''; ?>">ورود</a></li>
                    <li><a href="/signup" class="<?php echo $current_page === 'register.php' ? 'active' : ''; ?>">ثبت نام</a></li>
                </ul>
            <?php endif; ?>

        </div>

        <!-- Social Media Links (for Mobile View) -->
        <div class="mobile-social-links">
            <a href="#" class="social-icon support">
                <i class="fas fa-headset"></i>
            </a>
            <a href="https://wa.me/YOUR_WHATSAPP_NUMBER" target="_blank" class="social-icon whatsapp">
                <i class="fab fa-whatsapp"></i>
            </a>
            <a href="https://t.me/YOUR_TELEGRAM_USERNAME" target="_blank" class="social-icon telegram">
                <i class="fab fa-telegram-plane"></i>
            </a>
            <a href="https://instagram.com/YOUR_INSTAGRAM_USERNAME" target="_blank" class="social-icon instagram">
                <i class="fab fa-instagram"></i>
            </a>
        </div>

       <!-- لینک‌های منو -->
<div class="flex-1 flex justify-center space-x-2 px-0">
    <ul class="menu menu-horizontal px-0 space-x-4">
        <li>
            <a href="/" class="<?php echo $current_page === 'index.php' ? 'active' : ''; ?>">
                <span class="inline-block">خانه</span>
            </a>
        </li>
        <li>
            <a href="/shop" class="<?php echo $current_page === 'shop.php' ? 'active' : ''; ?>">
                <span class="inline-block">فروشگاه</span>
            </a>
        </li>
        <li>
            <a href="/rules" class="<?php echo $current_page === 'rules.php' ? 'active' : ''; ?>">
                <span class="inline-block">قوانین و مقررات</span>
            </a>
        </li>
        <li>
            <a href="/contact_us" class="<?php echo $current_page === 'contact_us.php' ? 'active' : ''; ?>">
                <span class="inline-block">تماس با ما</span>
            </a>
        </li>
    </ul>
</div>

        <!-- دکمه ورود/ثبت‌نام یا پروفایل و سبد خرید -->
        <div class="user-section">
            <!-- باکس جستجو -->
            <div class="search-box">
                <div class="search-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.9,16.776A10.539,10.539,0,1,0,16.776,18.9l5.1,5.1L24,21.88ZM10.5,18A7.5,7.5,0,1,1,18,10.5,7.507,7.507,0,0,1,10.5,18Z"></path>
                    </svg>
                </div>
                <input type="text" class="search-input" placeholder="جستجو...">
                <div class="search-results"></div>
            </div>
            
            <?php if (isset($_SESSION['user_id'])): ?>
            <!-- اگر کاربر وارد شده باشد -->
            <a href="/cart" class="cart-button">
                <i class="fas fa-shopping-cart"></i>
                <span>سبد خرید</span>
            </a>

            <div class="profile-dropdown relative">
                <button id="profileButton" class="profile-button">
                    <img src="image/profile_user.jpg" alt="Profile">
                </button>
                <div id="dropdownMenu" class="dropdown-menu">
                    <a href="/profile">حساب کاربری</a>
                    <a href="/orders">سفارشات</a>
                    <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                    <a href="/admin_panel">مدیریت سیستم</a>
                    <?php endif; ?>
                    <a href="/logout">خروج</a>
                </div>
            </div>

            <?php else: ?>
            <!-- اگر کاربر وارد نشده باشد -->
            <a href="/login" class="login-button">
                <div class="text-container">
                    <span>ورود/ثبت‌نام</span>
                </div>
            </a>
            <?php endif; ?>
        </div>
        </nav>
    </header>

    <!-- نوار ناوبری موبایل -->
    <div class="mobile-bottom-nav">
        <div class="button-container">
            <a href="/" class="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 1024 1024" stroke-width="0" fill="currentColor" stroke="currentColor" class="icon">
                    <path d="M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 0 0-44.4 0L77.5 505a63.9 63.9 0 0 0-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0 0 18.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"></path>
                </svg>
                <span>خانه</span>
            </a>
            <button class="button" id="mobileSearchButton">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" aria-hidden="true" viewBox="0 0 24 24" stroke-width="2" fill="none" stroke="currentColor" class="icon">
                    <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" stroke-linejoin="round" stroke-linecap="round"></path>
                </svg>
                <span>جستجو</span>
            </button>
            <?php if (isset($_SESSION['user_id'])): ?>
            <a href="/cart" class="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" stroke-linejoin="round" stroke-linecap="round" viewBox="0 0 24 24" stroke-width="2" fill="none" stroke="currentColor" class="icon">
                    <circle r="1" cy="21" cx="9"></circle>
                    <circle r="1" cy="21" cx="20"></circle>
                    <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                </svg>
                <span>سبد خرید</span>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- باکس جستجوی موبایل -->
    <div class="mobile-search-box" id="mobileSearchBox">
        <div class="mobile-search-header">
            <h3>جستجو</h3>
            <button class="close-search">&times;</button>
        </div>
        <input type="text" class="mobile-search-input" placeholder="جستجو...">
        <div class="mobile-search-results"></div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Dropdown menu functionality
        const profileButton = document.getElementById("profileButton");
        const dropdownMenu = document.getElementById("dropdownMenu");

        if (profileButton && dropdownMenu) {
            profileButton.addEventListener("click", function(event) {
                event.preventDefault();
                event.stopPropagation();
                dropdownMenu.classList.toggle("active");
            });

            // Close dropdown when clicking outside
            document.addEventListener("click", function(event) {
                if (!dropdownMenu.contains(event.target) && !profileButton.contains(event.target)) {
                    dropdownMenu.classList.remove("active");
                }
            });

            // Close dropdown when clicking a link
            const dropdownLinks = dropdownMenu.querySelectorAll('a');
            dropdownLinks.forEach(link => {
                link.addEventListener("click", function() {
                    dropdownMenu.classList.remove("active");
                });
            });
        }

        // Search box functionality
        const searchBox = document.querySelector('.search-box');
        const searchInput = document.querySelector('.search-input');
        const searchResults = document.querySelector('.search-results');
        let debounceTimerDesktop;

        // باز کردن باکس جستجو با کلیک
        if (searchBox && searchInput) {
            searchBox.addEventListener('click', function () {
                searchBox.classList.toggle('active');
                if (searchBox.classList.contains('active')) {
                    searchInput.focus(); // فوکوس روی input هنگام باز شدن
                    if (searchInput.value.trim() === '') {
                        fetchRecentSearches(searchResults);
                    }
                }
            });

            // بستن باکس جستجو وقتی کاربر خارج از آن کلیک کند
            document.addEventListener('click', function (event) {
                if (!searchBox.contains(event.target)) {
                    searchBox.classList.remove('active');
                }
            });

            // اضافه کردن قابلیت جستجو در نسخه دسکتاپ
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                
                // پاک کردن تایمر قبلی برای جلوگیری از درخواست‌های متعدد
                clearTimeout(debounceTimerDesktop);
                
                if (query === '') {
                    fetchRecentSearches(searchResults);
                    return;
                }
                
                // تأخیر در ارسال درخواست جستجو (300 میلی‌ثانیه)
                debounceTimerDesktop = setTimeout(() => {
                    searchProducts(query, searchResults);
                }, 300);
            });

            // ارسال فرم با فشردن کلید Enter در نسخه دسکتاپ
            searchInput.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    const query = this.value.trim();
                    if (query !== '') {
                        // ذخیره جستجو در session
                        saveSearchToSession(query);
                        // هدایت به صفحه فروشگاه
                        window.location.href = `/shop?q=${encodeURIComponent(query)}`;
                    }
                }
            });
        }

        // اضافه کردن عملکرد منوی موبایل
        const hamburgerMenu = document.querySelector('.hamburger-menu');
        const mobileMenu = document.querySelector('.mobile-menu');
        const socialLinks = document.querySelector('.mobile-social-links'); // Select the social links container
        const closeMenu = document.querySelector('.close-menu');
        const overlay = document.querySelector('.overlay');
        const body = document.body;

        function toggleMenu() {
            if (hamburgerMenu) hamburgerMenu.classList.toggle('active');
            if (mobileMenu) mobileMenu.classList.toggle('active');
            if (socialLinks) socialLinks.classList.toggle('active'); // Toggle active class on social links
            if (overlay) overlay.classList.toggle('active');
            body.style.overflow = body.style.overflow === 'hidden' ? '' : 'hidden';
        }

        if (hamburgerMenu) hamburgerMenu.addEventListener('click', toggleMenu);
        if (closeMenu) closeMenu.addEventListener('click', toggleMenu);
        if (overlay) overlay.addEventListener('click', toggleMenu);

        // بستن منو با کلیک روی لینک‌ها
        if (mobileMenu) {
            const mobileMenuLinks = mobileMenu.querySelectorAll('a');
            mobileMenuLinks.forEach(link => {
                link.addEventListener('click', () => {
                    toggleMenu();
                });
            });
        }

        // بستن منو با کلید ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
                toggleMenu();
            }
        });

        // جلوگیری از scroll صفحه هنگام scroll در منو
        if (mobileMenu) {
            mobileMenu.addEventListener('wheel', (e) => {
                e.stopPropagation();
            });
        }

        // اضافه کردن عملکرد جستجوی موبایل
        const mobileSearchButton = document.getElementById('mobileSearchButton');
        const mobileSearchBox = document.getElementById('mobileSearchBox');
        const closeSearch = document.querySelector('.close-search');
        const mobileSearchInput = document.querySelector('.mobile-search-input');
        const mobileSearchResults = document.querySelector('.mobile-search-results');
        
        let debounceTimer;

        if (mobileSearchButton && mobileSearchBox) {
            mobileSearchButton.addEventListener('click', function() {
                mobileSearchBox.classList.add('active');
                if (mobileSearchInput) {
                    mobileSearchInput.focus();
                    if (mobileSearchInput.value.trim() === '') {
                        fetchRecentSearches(mobileSearchResults);
                    }
                }
            });
        }

        if (closeSearch) {
            closeSearch.addEventListener('click', function() {
                if (mobileSearchBox) mobileSearchBox.classList.remove('active');
            });
        }

        // جستجوی پویا با تایپ کاربر
        if (mobileSearchInput && mobileSearchResults) {
            mobileSearchInput.addEventListener('input', function() {
                const query = this.value.trim();
                
                // پاک کردن تایمر قبلی برای جلوگیری از درخواست‌های متعدد
                clearTimeout(debounceTimer);
                
                if (query === '') {
                    fetchRecentSearches(mobileSearchResults);
                    return;
                }
                
                // تأخیر در ارسال درخواست جستجو (300 میلی‌ثانیه)
                debounceTimer = setTimeout(() => {
                    searchProducts(query, mobileSearchResults);
                }, 300);
            });

            // ارسال فرم با فشردن کلید Enter
            mobileSearchInput.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    const query = this.value.trim();
                    if (query !== '') {
                        // ذخیره جستجو در session
                        saveSearchToSession(query);
                        // هدایت به صفحه فروشگاه
                        window.location.href = `/shop?q=${encodeURIComponent(query)}`;
                    }
                }
            });
        }

        // تابع جستجوی محصولات
        function searchProducts(query, resultsContainer) {
            fetch(`search_api.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data, query, resultsContainer);
                })
                .catch(error => {
                    console.error('Error searching products:', error);
                });
        }

        // تابع دریافت جستجوهای اخیر
        function fetchRecentSearches(resultsContainer) {
            // نمایش جستجوهای اخیر از session
            if (window.sessionStorage.getItem('recent_searches')) {
                try {
                    const recentSearches = JSON.parse(window.sessionStorage.getItem('recent_searches'));
                    
                    if (recentSearches && recentSearches.length > 0) {
                        let html = '<div class="search-section"><h3>جستجوهای اخیر</h3><ul>';
                        
                        recentSearches.forEach(term => {
                            html += `<li>
                                <div class="recent-search-row">
                                    <a href="/shop?q=${encodeURIComponent(term)}" class="search-item">
                                        <i class="fas fa-history"></i>
                                        <span>${term}</span>
                                    </a>
                                    <button class="remove-search" data-term="${term}"><i class="fas fa-times"></i></button>
                                </div>
                            </li>`;
                        });
                        
                        html += '</ul></div>';
                        resultsContainer.innerHTML = html;
                        resultsContainer.style.display = 'block';
                        
                        // اضافه کردن event listener برای دکمه‌های حذف
                        const removeButtons = resultsContainer.querySelectorAll('.remove-search');
                        removeButtons.forEach(button => {
                            button.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                const term = this.getAttribute('data-term');
                                removeSearchFromSession(term);
                                fetchRecentSearches(resultsContainer);
                            });
                        });
                    } else {
                        resultsContainer.innerHTML = '<div class="no-results">هیچ جستجوی اخیری وجود ندارد</div>';
                        resultsContainer.style.display = 'block';
                    }
                } catch (e) {
                    console.error('Error parsing recent searches:', e);
                    resultsContainer.innerHTML = '';
                    resultsContainer.style.display = 'none';
                }
            } else {
                // اگر جستجوی اخیری وجود نداشت
                resultsContainer.innerHTML = '<div class="no-results">هیچ جستجوی اخیری وجود ندارد</div>';
                resultsContainer.style.display = 'block';
            }
        }

        // اضافه کردن تابع حذف جستجو از session
        function removeSearchFromSession(term) {
            if (window.sessionStorage.getItem('recent_searches')) {
                try {
                    let recentSearches = JSON.parse(window.sessionStorage.getItem('recent_searches'));
                    // حذف جستجوی مورد نظر
                    recentSearches = recentSearches.filter(item => item !== term);
                    // ذخیره مجدد در session
                    window.sessionStorage.setItem('recent_searches', JSON.stringify(recentSearches));
                } catch (e) {
                    console.error('Error removing search term:', e);
                }
            }
        }

        // تابع نمایش نتایج جستجو
        function displaySearchResults(data, query, resultsContainer) {
            let html = '';
            
            // نمایش محصولات
            if (data.results && data.results.length > 0) {
                html += '<div class="search-section"><h3>محصولات</h3><ul>';
                
                data.results.forEach(product => {
                    let imgHtml = '';
                    if (product.image) {
                        imgHtml = `<img src="data:image/jpeg;base64,${product.image}" alt="${product.name}" class="search-result-image">`;
                    } else {
                        imgHtml = '<div class="search-result-no-image"><i class="fas fa-image"></i></div>';
                    }
                    
                    html += `<li>
                        <a href="/card?id=${product.id}" class="search-item">
                            ${imgHtml}
                            <div class="search-item-details">
                                <span class="search-item-name">${product.name}</span>
                                <span class="search-item-price">${formatPrice(product.price)} تومان</span>
                            </div>
                        </a>
                    </li>`;
                });
                
                html += '</ul></div>';
            }
            
            // نمایش دسته‌بندی‌ها
            if (data.categories && data.categories.length > 0) {
                html += '<div class="search-section"><h3>دسته‌بندی‌ها</h3><ul>';
                
                data.categories.forEach(category => {
                    html += `<li>
                        <a href="/shop?category=${encodeURIComponent(category)}" class="search-item">
                            <i class="fas fa-tag"></i>
                            <span>${category}</span>
                        </a>
                    </li>`;
                });
                
                html += '</ul></div>';
            }
            
            // لینک به صفحه نتایج کامل
            html += `<div class="view-all-results">
                <a href="/shop?q=${encodeURIComponent(query)}">مشاهده همه نتایج جستجو</a>
            </div>`;
            
            // اگر هیچ نتیجه‌ای یافت نشد
            if ((!data.results || data.results.length === 0) && (!data.categories || data.categories.length === 0)) {
                html = '<div class="no-results">نتیجه‌ای یافت نشد</div>';
            }
            
            resultsContainer.innerHTML = html;
            resultsContainer.style.display = 'block';
        }

        // فرمت کردن قیمت
        function formatPrice(price) {
            return new Intl.NumberFormat('fa-IR').format(price);
        }

        // ذخیره جستجو در session
        function saveSearchToSession(query) {
            let recentSearches = [];
            
            if (window.sessionStorage.getItem('recent_searches')) {
                try {
                    recentSearches = JSON.parse(window.sessionStorage.getItem('recent_searches'));
                } catch (e) {
                    console.error('Error parsing recent searches:', e);
                    recentSearches = [];
                }
            }
            
            // حذف جستجوی تکراری (اگر وجود داشت)
            recentSearches = recentSearches.filter(term => term !== query);
            
            // اضافه کردن جستجوی جدید در ابتدا
            recentSearches.unshift(query);
            
            // محدود کردن تعداد جستجوهای اخیر به 5
            if (recentSearches.length > 5) {
                recentSearches = recentSearches.slice(0, 5);
            }
            
            window.sessionStorage.setItem('recent_searches', JSON.stringify(recentSearches));
        }

        // اضافه کردن event listener برای بستن کادر جستجو با کلیک خارج از آن
        document.addEventListener('click', function(event) {
            // بررسی کلیک خارج از باکس جستجوی دسکتاپ
            if (searchBox && searchResults && !searchBox.contains(event.target)) {
                searchBox.classList.remove('active');
                searchResults.style.display = 'none';
            }
            
            // بررسی کلیک خارج از باکس جستجوی موبایل
            if (mobileSearchBox && mobileSearchResults && !mobileSearchBox.contains(event.target) && 
                !mobileSearchButton.contains(event.target)) {
                mobileSearchBox.classList.remove('active');
                mobileSearchResults.style.display = 'none';
            }
        });
    });
</script>
<div class="overlay"></div>
</body>
</html>
