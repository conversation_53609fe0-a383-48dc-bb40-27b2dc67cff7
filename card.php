<?php
// Start the session before any output
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'redirect-clean-url.php';

// DB connection & queries optimization
class DB
{
    private static $inst = null;
    private $conn;

    private function __construct()
    {
        $this->conn = new mysqli("localhost", "sibmobil_sibmobil_sibmobil_sbsh_user", "DqxEXxb6cQKxtwUGr2zj", "sibmobil_sibmobil_sibmobil_sbsh_database");
        if ($this->conn->connect_error)
            die("Connection failed");

        // برای پشتیبانی کامل از فارسی
        $this->conn->set_charset("utf8mb4");
    }

    public static function get()
    {
        if (self::$inst === null)
            self::$inst = new self();
        return self::$inst->conn;
    }
}


// Get product data
$db = DB::get();
$pid = (int) ($_GET['id'] ?? 0);

// Fetch main product
$prod = $db->prepare("SELECT * FROM products WHERE id = ?");
$prod->bind_param("i", $pid);
$prod->execute();
$product = $prod->get_result()->fetch_assoc();

// نرخ تبدیل درهم به تومان - باید با فایل shop.php هماهنگ باشد
$aed_to_toman_rate = 16000;

// محاسبه قیمت نهایی و درصد تخفیف با در نظر گرفتن هر دو نوع قیمت
$price_aed = $product['product_price_aed'] ?? null;
$discount_aed = $product['discount_price_aed'] ?? null;
$price_toman = $product['product_price'] ?? 0;
$discount_toman = $product['discount_price'] ?? 0;

$final_price = 0;
$old_price = 0;
$has_discount = false;

if ($price_aed !== null && $price_aed > 0) {
    $final_price = $price_aed * $aed_to_toman_rate;
    if ($discount_aed !== null && $discount_aed > 0) {
        $old_price = $final_price;
        $final_price = $discount_aed * $aed_to_toman_rate;
        $has_discount = true;
    }
} elseif ($price_toman > 0) {
    $final_price = $price_toman;
    if ($discount_toman > 0) {
        $old_price = $final_price;
        $final_price = $discount_toman;
        $has_discount = true;
    }
}

$discount_percentage = 0;
if ($has_discount && $old_price > 0) {
    $discount_percentage = round((($old_price - $final_price) / $old_price) * 100);
}

$product_name = $product['product_name'] ?? '';
$product_description = $product['product_description'] ?? '';
$product_price = $final_price; // for backward compatibility if used elsewhere
$product_currency = "IRR";
$product_image_url = "";
if (!empty($imgs)) {
    $img_stmt = $db->prepare("SELECT id FROM product_images WHERE product_id = ? ORDER BY id ASC LIMIT 1");
    $img_stmt->bind_param("i", $pid);
    $img_stmt->execute();
    $img_result = $img_stmt->get_result()->fetch_assoc();
    if ($img_result) {
        $product_image_url = "https://sibmobileshiraz.ir/image.php?id=" . $image_id;
    }
    $img_stmt->close();
}
$product_url = "https://sibmobileshiraz.ir/card.php?id=" . $pid;

// Fetch product images
$imgs = [];
if ($img_stmt = $db->prepare("SELECT image FROM product_images WHERE product_id = ? LIMIT 5")) {
    $img_stmt->bind_param("i", $pid);
    $img_stmt->execute();
    $result = $img_stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $imgs[] = base64_encode($row['image']);
    }
    $img_stmt->close();
}

// Fetch random products
$similar = [];
if (
    $sim_stmt = $db->prepare("
    SELECT p.*, pi.image
    FROM products p
    LEFT JOIN (
        SELECT pi1.product_id, pi1.image
        FROM product_images pi1
        INNER JOIN (
            SELECT product_id, MIN(id) as min_id
            FROM product_images
            GROUP BY product_id
        ) pi2 ON pi1.product_id = pi2.product_id AND pi1.id = pi2.min_id
    ) pi ON p.id = pi.product_id
    WHERE p.id != ?
    ORDER BY RAND()
    LIMIT 10"
    )
) {
    $sim_stmt->bind_param("i", $pid);
    $sim_stmt->execute();
    $similar = $sim_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $sim_stmt->close();
}
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($product['product_name'] ?? '') ?></title>
    <?php
$image_id = 0;

// اگر عکسی موجود بود یکی رو انتخاب کن
if (!empty($imgs)) {
    // پیدا کردن ID عکس (باید مقدار واقعی از DB بیاد)
    $img_stmt = $db->prepare("SELECT id FROM product_images WHERE product_id = ? ORDER BY id ASC LIMIT 1");
    $img_stmt->bind_param("i", $pid);
    $img_stmt->execute();
    $img_result = $img_stmt->get_result();
    if ($img_row = $img_result->fetch_assoc()) {
        $image_id = $img_row['id'];
    }
    $img_stmt->close();
}

$product_image_url = $image_id > 0 ? "https://sibmobileshiraz.ir/image.php?id=" . $image_id : null;
?>

<script type="application/ld+json">
{
  "@context": "https://schema.org/",
  "@type": "Product",
  "name": "<?= htmlspecialchars($product['product_name']) ?>",
  "image": "<?= $product_image_url ?>",
  "description": "<?= htmlspecialchars(mb_substr($product['product_description'], 0, 160)) ?>",
  "sku": "SKU<?= $product['id'] ?>",
  "offers": {
    "@type": "Offer",
    "priceCurrency": "IRR",
"price": "<?= $final_price ?>",
    "availability": "https://schema.org/<?= $product['stock_quantity'] > 0 ? 'InStock' : 'OutOfStock' ?>",
    "url": "https://sibmobileshiraz.ir/card.php?id=<?= $product['id'] ?>"
  }
}
</script>
    <!-- فونت و استایل‌های ضروری اولیه برای جلوگیری از FOUC -->
    <style>
        /* فونت سمیم به صورت درون‌خطی */
        @font-face {
            font-family: 'Samim';
            src: url('assets/fonts/Samim.eot');
            src: url('assets/fonts/Samim.eot?#iefix') format('embedded-opentype'),
                 url('assets/fonts/Samim.woff2') format('woff2'),
                 url('assets/fonts/Samim.woff') format('woff'),
                 url('assets/fonts/Samim.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        html, body {
            font-family: 'Samim', sans-serif;
            visibility: hidden;
        }

        .wf-active body {
            visibility: visible;
        }

        /* استایل برای جلوگیری از FOUC در اسلایدر */
        .swiper {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .swiper-initialized {
            opacity: 1;
        }

        /* حالت اسکلتونی برای اسلایدر قبل از لود شدن */
        .similar-products-container {
            min-height: 350px;
            position: relative;
        }

        /* اندازه‌های پیش‌فرض برای جلوگیری از پرش محتوا */
        .similar-products {
            min-height: 300px;
        }

        .swiper-slide {
            height: 300px;
        }

        /* اسکلتون لودینگ برای اسلایدر */
        .skeleton-loading .swiper-slide {
            background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 1rem;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* اندازه‌های معین برای حل مشکل جابجایی */
        .product-card {
            display: none;
        }

        .swiper-initialized .product-card {
            display: flex;
        }
    </style>
    <!-- پیش‌بارگذاری فونت‌ها -->
    <link rel="preload" href="assets/fonts/Samim.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="assets/fonts/Samim.woff" as="font" type="font/woff" crossorigin>

    <!-- CSS های ضروری ابتدا لود شوند -->
    <link rel="stylesheet" href="assets/css/cart_style.css?v=1.3">
    <link rel="stylesheet" href="assets/webfonts/fontawsome.css">
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css" />

    <!-- اسکریپت Web Font Loader برای مدیریت بهتر بارگذاری فونت -->
    <script>
        // مدیریت بارگذاری فونت به شکل بهینه
        (function() {
            if (sessionStorage.fontsLoaded) {
                document.documentElement.classList.add('wf-active');
                return;
            }

            document.documentElement.classList.add('wf-loading');

            // تشخیص زمان بارگذاری فونت
            var font = new FontFace('Samim', "url('assets/fonts/Samim.woff2') format('woff2')");

            font.load().then(function() {
                document.fonts.add(font);
                document.documentElement.classList.remove('wf-loading');
                document.documentElement.classList.add('wf-active');
                sessionStorage.fontsLoaded = true;
                document.body.style.visibility = 'visible';
            }).catch(function() {
                document.documentElement.classList.remove('wf-loading');
                document.documentElement.classList.add('wf-inactive');
                document.body.style.visibility = 'visible';
            });
        })();
    </script>

    <!-- اسکریپت‌های ضروری -->
    <script src="assets/css/tailwind.css"></script>
    <link href="assets/css/daisy.css" rel="stylesheet" />
    <script src="assets/js/sweetalert.js"></script>

    <style>
        /* تعریف فونت سمیم */
        @font-face {
            font-family: 'Samim';
            src: url('assets/fonts/Samim.eot');
            src: url('assets/fonts/Samim.eot?#iefix') format('embedded-opentype'),
                 url('assets/fonts/Samim.woff2') format('woff2'),
                 url('assets/fonts/Samim.woff') format('woff'),
                 url('assets/fonts/Samim.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        /* استایل های اختصاصی صفحه محصول */
        .summary-card {
            animation: scaleIn 0.5s ease-out;
            background: linear-gradient(145deg, #ffffff, #f5f7fa);
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            padding: 1rem !important;
        }

        .summary-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .img-container {
            height: 340px;
            position: relative;
            overflow: hidden;
            background: #f8f8f8;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .img-container:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .img-container img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.5s ease;
        }

        .img-container:hover img {
            transform: translate(-50%, -50%) scale(1.05);
        }

        .thumb {
            width: 70px;
            height: 70px;
            cursor: pointer;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
            object-fit: contain;
            background: #f8fafc;
            padding: 5px;
        }

        .thumb:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .thumb.active {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }

        .thumbs-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 340px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #e5e7eb transparent;
        }

        .thumbs-container::-webkit-scrollbar {
            width: 4px;
        }

        .thumbs-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .thumbs-container::-webkit-scrollbar-thumb {
            background-color: #e5e7eb;
            border-radius: 10px;
        }

        /* اسلایدر محصولات مشابه - استایل های جدید */
        .similar-products-container {
            background: linear-gradient(to bottom, #f8fafc, #ffffff);
            padding: 2rem 0;
            border-radius: 1rem;
            margin-top: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }

        .similar-products .swiper-slide {
            height: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 1rem;
            overflow: hidden;
        }

        .similar-products .swiper-slide:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.15);
        }

        .product-card {
            border-radius: 1rem;
            background: white;
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }

        .product-card:hover {
            border-color: #3b82f6;
        }

        .product-card .img-wrapper {
            height: 180px;
            overflow: hidden;
            position: relative;
            background: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-card .img-wrapper img {
            max-height: 85%;
            max-width: 85%;
            object-fit: contain;
            transition: transform 0.5s ease;
        }

        .product-card:hover .img-wrapper img {
            transform: scale(1.08);
        }

        .product-card .content {
            padding: 1rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            position: relative;
        }

        .product-card .title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.75rem;
            line-height: 1.5;
            height: 3em;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .product-card .price-container {
            margin-top: auto;
        }

        .product-card .original-price {
            text-decoration: line-through;
            color: #94a3b8;
            font-size: 0.875rem;
        }

        .product-card .discount-price {
            font-weight: 700;
            color: #10b981;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .product-card .discount-badge {
            background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            position: absolute;
            top: 0.75rem;
            left: 0.75rem;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.25);
        }

        .product-card .stock-status {
            margin-top: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .product-card .in-stock {
            color: #3b82f6;
        }

        .product-card .out-of-stock {
            color: #ef4444;
        }

        /* استایل های نشانگر اسلایدر */
        .similar-section-title {
            position: relative;
            margin-bottom: 2rem;
            display: inline-block;
        }


        .swiper-pagination {
            bottom: 0px !important;
            position: relative;
            margin-top: 2rem;
        }

        .swiper-pagination-bullet {
            width: 10px !important;
            height: 10px !important;
            background: #e5e7eb !important;
            opacity: 1 !important;
            transition: all 0.3s ease;
        }

        .swiper-pagination-bullet-active {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            width: 20px !important;
            border-radius: 5px !important;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: white !important;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            width: 2.5rem !important;
            height: 2.5rem !important;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.25);
        }

        .swiper-button-next:hover,
        .swiper-button-prev:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 10px rgba(59, 130, 246, 0.3);
        }

        .swiper-button-next::after,
        .swiper-button-prev::after {
            font-size: 1rem !important;
            font-weight: bold;
        }

        @media (max-width: 640px) {
            .swiper-button-next,
            .swiper-button-prev {
                width: 2rem !important;
                height: 2rem !important;
                display: none !important;
            }

            .swiper-button-next::after,
            .swiper-button-prev::after {
                font-size: 0.75rem !important;
            }
        }

        /* ریسپانسیو */
        @media (max-width: 768px) {
            .img-container {
                height: 280px;
            }

            .thumb {
                width: 60px;
                height: 60px;
            }

            .thumbs-container {
                height: 280px;
            }

            .summary-card {
                position: static !important;
                margin-top: 1rem !important;
            }
        }

        @media (max-width: 640px) {
            .img-container {
                height: 230px;
            }

            .thumb {
                width: 50px;
                height: 50px;
            }

            .thumbs-container {
                height: 230px;
                padding-right: 0;
            }

            /* حالت افقی تصاویر بندانگشتی در موبایل */
            .flex-responsive {
                flex-direction: column-reverse;
            }

            .thumbs-container-mobile {
                flex-direction: row;
                height: auto;
                overflow-x: auto;
                overflow-y: hidden;
                padding-bottom: 5px;
                margin-top: 8px;
            }
        }

        /* بهبود نمایش در موبایل */
        @media (max-width: 480px) {
            .container {
                padding-left: 12px;
                padding-right: 12px;
            }

            h1 {
                font-size: 1.5rem;
            }

            h2 {
                font-size: 1.25rem;
            }
        }

        /* استایل‌های سفارشی دکمه افزودن به سبد خرید */
        .add-to-cart {
            background-image: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
        }

        .add-to-cart:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 7px 14px rgba(59, 130, 246, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08) !important;
            background-image: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
        }

        /* استایل توضیحات محصول */
        .product-description {
            line-height: 1.8;
        }

        /* استایل خط جداکننده */
        .divider {
            border-bottom: 1px solid #f3f4f6;
            margin: 0.5rem 0;
        }

        /* استایل‌های summary-row دقیقاً مشابه سبد خرید */
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .summary-row:last-child {
            border-bottom: none;
        }

        /* بخش‌های مختلف سایدبار */
        .sidebar-section {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .sidebar-section:last-child {
            border-bottom: none;
            padding-bottom: 0;
            margin-bottom: 0;
        }

        /* کاهش فاصله در ساید بار */
        .summary-card {
            padding: 1rem !important;
        }

        .summary-card h3 {
            margin-bottom: 0.5rem !important;
        }

        .summary-label {
            color: #4b5563;
            font-weight: 500;
        }

        .summary-value {
            color: #111827;
            font-weight: 600;
        }

        .summary-total {
            color: #059669;
            font-size: 1.25rem;
            font-weight: 700;
        }

        /* جهت متن انگلیسی و فارسی */
        .text-en {
            direction: ltr;
            text-align: left;
        }

        /* برای متن‌های ترکیبی که بسته به زبان شروع متن جهت آنها تعیین می‌شود */
        [dir="auto"] {
            unicode-bidi: plaintext;
        }

        /* استایل دکمه مشابه دکمه دسته بندی در فروشگاه */
        .category-style-button {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            border: none !important;
            color: white !important;
            font-weight: 500 !important;
            border-radius: 0.5rem !important;
            transition: all 0.3s ease !important;
            position: relative;
            overflow: hidden;
        }

        .category-style-button:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 6px rgba(37, 99, 235, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        .category-style-button:active {
            transform: translateY(1px) !important;
            box-shadow: 0 1px 2px rgba(37, 99, 235, 0.2), 0 1px 1px rgba(0, 0, 0, 0.08) !important;
        }

        .category-style-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-100%);
            animation: shine 5s infinite;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%);
            }
            15% {
                transform: translateX(100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* استایل‌های SweetAlert */
        .swal2-popup {
            border-radius: 1rem !important;
            padding: 2rem !important;
            font-family: 'Samim', sans-serif !important;
        }

        .swal2-title {
            color: #1e293b !important;
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            margin-bottom: 1rem !important;
            font-family: 'Samim', sans-serif !important;
        }

        .swal2-html-container {
            color: #64748b !important;
            font-size: 1rem !important;
            margin-bottom: 1.5rem !important;
            font-family: 'Samim', sans-serif !important;
        }

        .swal2-actions {
            gap: 0.5rem !important;
        }

        .swal2-confirm {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            border-radius: 0.75rem !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            font-family: 'Samim', sans-serif !important;
        }

        .swal2-confirm:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3) !important;
        }

        .swal2-cancel {
            background: linear-gradient(135deg, #6b7280 0%, #374151 100%) !important;
            border-radius: 0.75rem !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            font-family: 'Samim', sans-serif !important;
        }

        .swal2-cancel:hover {
            background: linear-gradient(135deg, #4b5563 0%, #1f2937 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 6px rgba(75, 85, 99, 0.3) !important;
        }

        .swal2-actions button {
            font-size: 0.875rem !important;
            border: none !important;
            cursor: pointer !important;
        }

        /* استایل‌های Toast */
        .swal2-toast {
            border-radius: 0.75rem !important;
            padding: 1rem !important;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
            font-family: 'Samim', sans-serif !important;
        }

        .swal2-toast .swal2-title {
            font-size: 1rem !important;
            margin-bottom: 0.5rem !important;
        }

        .swal2-toast .swal2-html-container {
            font-size: 0.875rem !important;
            margin-bottom: 0 !important;
        }

        .swal2-toast .swal2-actions {
            margin-top: 0.5rem !important;
        }

        .swal2-toast .swal2-confirm,
        .swal2-toast .swal2-cancel {
            padding: 0.5rem 1rem !important;
            font-size: 0.75rem !important;
        }
    </style>
</head>

<body class="bg-gray-50">
    <?php include('navbar.php'); ?>

    <!-- Main Product Section -->
    <div class="container mx-auto p-4">
        <div class="grid md:grid-cols-3 grid-cols-1 gap-6">
            <!-- بخش اصلی محصول -->
            <div class="md:col-span-2 bg-white p-6 rounded-lg shadow h-auto">
                <!-- تصویر و اطلاعات محصول در یک ردیف -->
                <div class="grid md:grid-cols-2 grid-cols-1 gap-6">
                    <!-- تصویر محصول -->
                    <div>
                        <div class="flex gap-4 flex-responsive">
                            <div class="thumbs-container thumbs-container-mobile">
                                <?php foreach ($imgs as $i => $img): ?>
                                    <img class="thumb <?= $i === 0 ? 'active' : '' ?>" src="data:image/jpeg;base64,<?= $img ?>"
                                        onclick="updateImg(this, <?= $i ?>)" alt="تصویر <?= $i + 1 ?>">
                                <?php endforeach; ?>
                            </div>

                            <div class="img-container flex-grow">
                                <img id="main-img" src="data:image/jpeg;base64,<?= $imgs[0] ?? '' ?>"
                                    alt="<?= htmlspecialchars($product['product_name'] ?? '') ?>"
                                    style="mix-blend-mode: multiply;"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- توضیحات محصول -->
                    <div class="space-y-4">
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4"><?= htmlspecialchars($product['product_name'] ?? '') ?></h1>
                        <p class="text-gray-700 product-description leading-relaxed"><?= nl2br(htmlspecialchars($product['product_description'] ?? '')) ?></p>
                        <?php if ($product['stock_quantity'] > 0): ?>
                            <div class="inline-block bg-blue-50 text-blue-600 px-4 py-2 rounded-lg text-sm mt-auto">
                                <i class="fas fa-shipping-fast ml-1"></i>
                                آماده ارسال از انبار سیب موبایل
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar for product info and purchase -->
            <div class="summary-card p-4 space-y-0 sticky top-4 h-fit">
                <!-- ارسال فوری -->
                <div class="sidebar-section">
                    <div class="flex items-center gap-2 text-green-700 font-medium">
                        <i class="fas fa-shipping-fast text-lg"></i>
                        <span class="text-base">ارسال فوری</span>
                    </div>
                </div>

                <!-- محاسبه قیمت -->
                <div class="sidebar-section">
                    <h3 class="text-base font-bold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-calculator ml-2 text-blue-600"></i>
                        محاسبه قیمت
                    </h3>

                    <?php if ($has_discount): ?>
                        <!-- قیمت اصلی با تخفیف -->
                        <div class="summary-row">
                            <span class="summary-label">قیمت اصلی:</span>
                            <span class="line-through text-gray-500 text-en">
                                <?= number_format($old_price) ?> تومان
                            </span>
                        </div>
                        <!-- تخفیف -->
                        <div class="summary-row">
                            <span class="summary-label">تخفیف ویژه:</span>
                            <div class="flex items-center gap-2">
                                <span class="text-green-600 font-bold text-en"><?= number_format($final_price) ?> تومان</span>
                                <?php if ($discount_percentage > 0): ?>
                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                    <?= $discount_percentage ?>٪
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- قیمت اصلی بدون تخفیف -->
                        <div class="summary-row">
                            <span class="summary-label">قیمت:</span>
                            <span class="summary-value text-en">
                                <?= number_format($final_price) ?> تومان
                            </span>
                        </div>
                    <?php endif; ?>
                </div>
                <br>
                <!-- موجودی محصول -->
                <div class="sidebar-section">
                    <h3 class="text-base font-bold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-box ml-2 text-blue-600"></i>
                        موجودی انبار
                    </h3>

                    <?php if ($product['stock_quantity'] > 0): ?>
                        <div class="flex items-center gap-2 text-blue-700">
                            <i class="fas fa-check-circle"></i>
                            <span dir="auto">موجود در انبار - <span class="text-en"><?= $product['stock_quantity'] ?></span> عدد</span>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center gap-2 text-red-700">
                            <i class="fas fa-times-circle"></i>
                            <span>ناموجود در انبار</span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- قیمت نهایی کالا -->
                <div class="sidebar-section">
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700 font-medium flex items-center">
                                <i class="fas fa-tag ml-2 text-green-600"></i>
                                قیمت نهایی:
                            </span>
                            <span class="summary-total text-en">
                                <?= number_format($final_price) ?> تومان
                            </span>
                        </div>
                    </div>
                </div>

                <!-- دکمه افزودن به سبد خرید -->
                <?php if ($product['stock_quantity'] > 0): ?>
                    <button
                        class="w-full px-6 py-3 rounded-lg shadow-md add-to-cart mt-2 category-style-button"
                        data-pid="<?= $product['id'] ?>">
                        <i class="fas fa-shopping-cart ml-2"></i>
                        افزودن به سبد خرید
                    </button>
                <?php else: ?>
                    <button
                        class="w-full px-6 py-3 bg-gray-400 text-white rounded-lg cursor-not-allowed mt-2 opacity-75"
                        disabled>
                        <i class="fas fa-ban ml-2"></i>
                        ناموجود در انبار
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Similar Products Section -->
    <?php if (!empty($similar)): ?>
        <div class="similar-products-container">
            <div class="container mx-auto px-4 my-6">
                <h2 class="text-xl md:text-2xl font-bold similar-section-title flex items-center">
                    <i class="fas fa-bolt ml-2 text-blue-600"></i>
                    محصولات مشابه
                </h2>

                <div class="swiper similar-products skeleton-loading">
                    <div class="swiper-wrapper">
                        <?php foreach ($similar as $sim): ?>
                            <div class="swiper-slide">
                                <?php
                                    // محاسبه قیمت برای محصولات مشابه
                                    $sim_price_aed = $sim['product_price_aed'] ?? null;
                                    $sim_discount_aed = $sim['discount_price_aed'] ?? null;
                                    $sim_price_toman = $sim['product_price'] ?? 0;
                                    $sim_discount_toman = $sim['discount_price'] ?? 0;

                                    $sim_final_price = 0;
                                    $sim_old_price = 0;
                                    $sim_has_discount = false;

                                    if ($sim_price_aed !== null && $sim_price_aed > 0) {
                                        $sim_final_price = $sim_price_aed * $aed_to_toman_rate;
                                        if ($sim_discount_aed !== null && $sim_discount_aed > 0) {
                                            $sim_old_price = $sim_final_price;
                                            $sim_final_price = $sim_discount_aed * $aed_to_toman_rate;
                                            $sim_has_discount = true;
                                        }
                                    } elseif ($sim_price_toman > 0) {
                                        $sim_final_price = $sim_price_toman;
                                        if ($sim_discount_toman > 0) {
                                            $sim_old_price = $sim_final_price;
                                            $sim_final_price = $sim_discount_toman;
                                            $sim_has_discount = true;
                                        }
                                    }

                                    $sim_discount_percent = 0;
                                    if ($sim_has_discount && $sim_old_price > 0) {
                                        $sim_discount_percent = round((($sim_old_price - $sim_final_price) / $sim_old_price) * 100);
                                    }
                                ?>
                                <a href="/card?id=<?= $sim['id'] ?>" class="product-card">
                                    <?php if ($sim_discount_percent > 0): ?>
                                        <div class="discount-badge">
                                            <?= $sim_discount_percent ?>٪ تخفیف
                                        </div>
                                    <?php endif; ?>

                                    <div class="img-wrapper">
                                        <img src="data:image/jpeg;base64,<?= base64_encode($sim['image']) ?>"
                                            alt="<?= htmlspecialchars($sim['product_name']) ?>"
                                            style="mix-blend-mode: multiply;"
                                        >
                                    </div>

                                    <div class="content">
                                        <h3 class="title">
                                            <?= htmlspecialchars($sim['product_name']) ?>
                                        </h3>

                                        <div class="price-container">
                                            <?php if ($sim_has_discount): ?>
                                                <div class="original-price text-en">
                                                    <?= number_format($sim_old_price) ?> تومان
                                                </div>
                                                <div class="discount-price text-en">
                                                    <?= number_format($sim_final_price) ?> تومان
                                                </div>
                                            <?php else: ?>
                                                <div class="discount-price text-en">
                                                    <?= number_format($sim_final_price) ?> تومان
                                                </div>
                                            <?php endif; ?>

                                            <div class="stock-status">
                                                <?php if ($sim['stock_quantity'] > 0): ?>
                                                    <div class="in-stock flex items-center gap-1">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                        </svg>
                                                        <span>موجود در انبار</span>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="out-of-stock flex items-center gap-1">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                        <span>ناموجود</span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</br>
    <?php include('footer.php'); ?>

    <script src="assets/js/swiper-bundle.min.js"></script>
    <script>
        // Image switcher
        function updateImg(thumb, idx) {
            document.getElementById('main-img').src = thumb.src;
            document.querySelectorAll('.thumb').forEach(t => t.classList.remove('active'));
            thumb.classList.add('active');
        }

        // Initialize Swiper and Cart handler
        document.addEventListener('DOMContentLoaded', () => {
            // Cart handler
            const btn = document.querySelector('.add-to-cart');
            if (btn) {
                btn.addEventListener('click', async () => {
                    try {
                        const res = await fetch('/add_to_cart', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: `product_id=${btn.dataset.pid}&quantity=1`
                        });

                        const data = await res.json();

                        Swal.fire({
                            icon: data.success ? 'success' : 'error',
                            title: data.success ? 'موفقیت' : 'خطا',
                            text: data.message,
                            confirmButtonText: 'باشه',
                            customClass: {
                                popup: 'swal2-popup',
                                title: 'swal2-title',
                                htmlContainer: 'swal2-html-container',
                                confirmButton: 'swal2-confirm',
                                cancelButton: 'swal2-cancel'
                            },
                            ...(data.success && {
                                preConfirm: () => window.location.href = data.redirect
                            })
                        });
                    } catch {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطا',
                            text: 'خطای غیرمنتظره‌ای رخ داد',
                            confirmButtonText: 'باشه',
                            customClass: {
                                popup: 'swal2-popup',
                                title: 'swal2-title',
                                htmlContainer: 'swal2-html-container',
                                confirmButton: 'swal2-confirm',
                                cancelButton: 'swal2-cancel'
                            }
                        });
                    }
                });
            }

            // اسلایدر محصولات مشابه با تنظیمات بهبود یافته
            const swiperElement = document.querySelector('.similar-products');
            if (swiperElement) {
                // تنظیم ابعاد پیش‌فرض برای جلوگیری از FOUC
                const slides = swiperElement.querySelectorAll('.swiper-slide');
                const slideWidth = window.innerWidth < 480 ? window.innerWidth * 0.8 :
                                  window.innerWidth < 640 ? window.innerWidth * 0.45 :
                                  window.innerWidth < 768 ? window.innerWidth * 0.3 :
                                  window.innerWidth * 0.25;

                slides.forEach(slide => {
                    slide.style.width = `${slideWidth}px`;
                });

                // راه‌اندازی اسلایدر با delay کوتاه
                setTimeout(() => {
                    const swiper = new Swiper(swiperElement, {
                        slidesPerView: 1.2,
                        spaceBetween: 16,
                        grabCursor: true,
                        loop: <?= count($similar) > 3 ? 'true' : 'false' ?>,
                        autoplay: {
                            delay: 4000,
                            disableOnInteraction: false,
                        },
                        pagination: {
                            el: '.swiper-pagination',
                            clickable: true,
                        },
                        navigation: window.innerWidth > 640 ? {
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev',
                        } : false,
                        breakpoints: {
                            480: { slidesPerView: <?= min(2, count($similar)) ?>, spaceBetween: 20 },
                            640: { slidesPerView: <?= min(2.5, count($similar)) ?>, spaceBetween: 24 },
                            768: { slidesPerView: <?= min(3, count($similar)) ?>, spaceBetween: 24 },
                            1024: { slidesPerView: <?= min(4, count($similar)) ?>, spaceBetween: 30 }
                        },
                        on: {
                            init: function() {
                                // حذف کلاس برای نمایش اسلایدر
                                swiperElement.style.opacity = 1;
                                swiperElement.classList.remove('skeleton-loading');

                                setTimeout(() => {
                                    window.dispatchEvent(new Event('resize'));
                                }, 100);
                            }
                        }
                    });
                }, 50);
            }
        });
    </script>
</body>

</html>
