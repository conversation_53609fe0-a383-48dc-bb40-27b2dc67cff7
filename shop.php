<?php
// شروع سشن اگر شروع نشده باشد
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'redirect-clean-url.php';

// اتصال به دیتابیس با پشتیبانی از UTF-8
$conn = new mysqli("localhost", "sibmobil_sibmobil_sibmobil_sbsh_user", "DqxEXxb6cQKxtwUGr2zj", "sibmobil_sibmobil_sibmobil_sbsh_database");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// تنظیم charset برای پشتیبانی از فارسی
$conn->set_charset("utf8mb4");

// نرخ تبدیل درهم به تومان
// برای تغییر قیمت‌ها، فقط این عدد را ویرایش کنید
$aed_to_toman_rate = 28020; // مثال: هر درهم 16,000 تومان



// Core config
define('ITEMS_PER_PAGE', 30);
$page = max(1, (int)($_GET['page'] ?? 1));
$offset = ($page - 1) * ITEMS_PER_PAGE;

// Price range config
$price_ranges = [
    0, 5000000, 10000000, 20000000, 30000000, 40000000,
    50000000, 60000000, 70000000, 80000000, 90000000, 100000000
];

// Query builder initialization
$params = [];
$types = '';
$where = ['1=1']; // Default condition

// Search query
$searchQuery = $_GET['q'] ?? '';
if (!empty($searchQuery)) {
    $searchQuery = $conn->real_escape_string($searchQuery);
    $where[] = "(product_name LIKE ? OR product_description LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
    $types .= 'ss';

    // Save search history for logged-in users
    if (isset($_SESSION['user_id'])) {
        $userId = (int)$_SESSION['user_id'];
        // Check if search_history table exists
        $checkTableSql = "SHOW TABLES LIKE 'search_history'";
        $tableResult = $conn->query($checkTableSql);

        if ($tableResult->num_rows > 0) {
            $saveSql = "INSERT INTO search_history (user_id, search_term) VALUES (?, ?)";
            $stmt = $conn->prepare($saveSql);
            $stmt->bind_param('is', $userId, $searchQuery);
            $stmt->execute();
        }
    }
}

// Category filter
$category = $_GET['category'] ?? '';
if (!empty($category) && $category !== 'all') {
    $where[] = 'category = ?';
    $params[] = $category;
    $types .= 's';
}

// Price filter
$min_price = (int)($_GET['min'] ?? 0);
$max_price = (int)($_GET['max'] ?? PHP_INT_MAX);

if ($min_price > 0 || $max_price < PHP_INT_MAX) {
    $rate = $aed_to_toman_rate;
    // کوئری قیمت برای پشتیبانی از هر دو نوع قیمت (درهم و تومان)
    $price_check_sql = "
    (CASE
        WHEN discount_price_aed > 0 AND discount_price_aed IS NOT NULL THEN discount_price_aed * $rate
        WHEN product_price_aed > 0 AND product_price_aed IS NOT NULL THEN product_price_aed * $rate
        WHEN discount_price > 0 AND discount_price IS NOT NULL THEN discount_price
        ELSE product_price
    END) BETWEEN ? AND ?";

    $where[] = $price_check_sql;
    $params[] = $min_price;
    $params[] = $max_price;
    $types .= 'dd';
}

// Construct WHERE clause
$where_sql = 'WHERE ' . implode(' AND ', $where);

// Discounted-only filter
$discounted = isset($_GET['discounted']) ? (int)$_GET['discounted'] : 0;
if ($discounted === 1) {
    // نمایش فقط محصولاتی که قیمت تخفیف دارند (درهم یا تومان)
    $where_sql .= (empty($where) ? ' WHERE ' : ' AND ')
        . "(discount_price_aed > 0 OR discount_price > 0)";
}

// Available-only filter
$available = isset($_GET['available']) ? (int)$_GET['available'] : 0;
if ($available === 1) {
    $where_sql .= (empty($where) ? ' WHERE ' : ' AND ') . "stock_quantity > 0";
}

// Count total products
$count_sql = "SELECT COUNT(*) as total FROM products $where_sql";
$stmt = $conn->prepare($count_sql);
if ($params) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$total_products = $stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_products / ITEMS_PER_PAGE);

// Fetch products with optimized JOIN
$products_sql = "SELECT p.*,
                 (SELECT pi.image FROM product_images pi WHERE pi.product_id = p.id LIMIT 1) AS image_blob
                 FROM products p
                 $where_sql
                 ORDER BY p.created_at DESC
                 LIMIT ? OFFSET ?";

// Add pagination params
$types .= 'ii';
$params[] = ITEMS_PER_PAGE;
$params[] = $offset;

$stmt = $conn->prepare($products_sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$products = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Fetch categories with caching
$categories = $conn->query("SELECT DISTINCT category FROM products")->fetch_all(MYSQLI_ASSOC);

// درج svg تومان به عنوان متغیر با کلاس مخصوص
$toman_svg = '<span class="toman-svg-inline" style="display:inline-block;vertical-align:middle;"><svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg" style="vertical-align:middle;"><path d="M2.32 6.118c.315-.154.552-.308.789-.539l.473-.692c.079-.308.158-.616.158-.924h7.492c.473 0 .867-.154 1.183-.385.236-.308.394-.692.394-1.231V.5h-1.025v1.77c0 .385-.237.616-.631.616H3.74V2.5c0-.385-.08-.616-.158-.924 0-.23-.158-.461-.316-.615a1.654 1.654 0 0 0-.473-.308C2.557.577 2.32.5 2.163.5c-.316 0-.553.077-.79.154C1.217.808.98.884.9 1.116c-.157.154-.315.307-.315.538-.079.231-.158.462-.158.693 0 .23 0 .462.08.693.078.23.157.384.236.538.157.077.315.231.552.308.236.077.473.077.867.077h.631c0 .154-.079.308-.079.462-.079.154-.236.308-.315.462-.079.076-.237.153-.473.23a1.515 1.515 0 0 1-.631.154H.27V6.35h1.025c.394-.077.71-.154 1.025-.231zm-.158-3.232c-.315 0-.473 0-.63-.154-.08-.077-.158-.231-.158-.462 0-.23.079-.462.157-.539.158-.077.316-.154.552-.154.237 0 .395.077.552.231.158.154.158.385.158.693v.462h-.63v-.077z" fill="#021959"/><path d="M10.442.5H7.445v.923h2.997V.5zM12.73 8.504c-.08-.231-.158-.385-.316-.539-.157-.154-.315-.308-.552-.385a1.515 1.515 0 0 0-.63-.154c-.237 0-.474.077-.71.154-.237.077-.395.231-.552.385-.158.154-.237.308-.316.539-.079.23-.079.462-.079.692v.231c0 .231-.079.308-.158.462-.157.077-.315.154-.552.154H8.55a.61.61 0 0 1-.394-.154c-.08-.154-.158-.308-.158-.462V5.733H6.973v3.848c0 .308 0 .539.079.693.078.154.157.308.315.462.158.076.237.23.473.23.158.077.316.077.552.077h.552c.158 0 .395-.077.552-.153.158-.078.395-.231.474-.462.157.23.315.384.552.461.236.077.473.154.788.154.473 0 .947-.153 1.183-.461.316-.308.473-.77.473-1.309a9.513 9.513 0 0 0-.236-.77zM11.23 9.966c-.236 0-.394-.077-.473-.154-.236-.154-.236-.308-.236-.539 0-.23.078-.384.157-.538.08-.154.316-.231.552-.231.237 0 .474.077.552.23.08.155.158.309.158.54-.079.461-.315.692-.71.692zM4.923 8.504c0 .23 0 .384-.08.615l-.236.462c-.158.154-.315.23-.473.308a1.607 1.607 0 0 1-.71.154h-.71c-.236 0-.473 0-.71-.077-.157-.154-.315-.231-.394-.385-.079-.154-.236-.308-.236-.462-.08-.154-.08-.384-.08-.615v-.847H.27v.924c0 .77.236 1.385.63 1.847.395.461.947.692 1.735.692h.79c.393 0 .709-.077 1.024-.23.316-.155.552-.309.789-.54.237-.23.394-.538.473-.846.08-.308.158-.616.158-1v-2.77l-1.025-.078.079 2.848z" fill="#021959"/><path d="M3.74 7.426H2.557v1.077H3.74V7.426z" fill="#021959"/></svg></span>';

// Start HTML output
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>فروشگاه</title>
    <script src="assets/css/tailwind.css"></script>
    <link rel="icon" type="image/png" href="/image/icons8-apple-48.png" />
    <link href="assets/css/daisy.css" rel="stylesheet" />
    <link href="assets/css/shop.css?v=<?= time() ?>" rel="stylesheet"/>
    <script src="assets/js/sweetalert.js"></script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .product-image-multiply {
            mix-blend-mode: multiply;
        }
    </style>
</head>
<body class="bg-white">
<?php include("navbar.php"); ?>
<div class="container mx-auto p-12 rtl-container" dir="rtl">
        <!-- اسلایدر دسته‌بندی‌ها برای موبایل -->
        <div class="category-slider md:hidden">
            <div class="flex">
                <button class="btn btn-outline"
                        onclick="navigateTo('/shop<?= isset($_GET['min']) ? '?min='.$_GET['min'].'&max='.$_GET['max'] : '' ?>', 'در حال بارگذاری همه محصولات...')"
                        <?= !isset($_GET['category']) ? 'style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05);"' : '' ?>>همه</button>
                <?php foreach ($categories as $cat): ?>
                    <button class="btn btn-outline"
                            onclick="navigateTo('/shop?<?= isset($_GET['min']) ? 'min='.$_GET['min'].'&max='.$_GET['max'].'&' : '' ?>category=<?= htmlspecialchars($cat['category']) ?>', 'در حال فیلتر بر اساس دسته‌بندی...')"
                            <?= (isset($_GET['category']) && $_GET['category'] === $cat['category']) ? 'style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05);"' : '' ?>>
                        <?= htmlspecialchars($cat['category']) ?>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="grid grid-cols-4 gap-6">
            <!-- سایدبار دسکتاپ -->
            <div class="col-span-1 bg-gray-50 border border-gray-200 rounded-lg p-4 sticky top-4 h-max hidden md:block">
                <h2 class="category-title">دسته‌بندی محصولات</h2>
                <div class="divider my-4 border-b border-gray-300"></div>
                <!-- باکس جستجو -->
                <div class="sidebar-search-box mt-4">
                    <input type="text" id="sidebar-search" placeholder="جستجوی محصولات..."
                           value="<?= htmlspecialchars($searchQuery) ?>"
                           onkeydown="if(event.key === 'Enter') { event.preventDefault(); performSearch('sidebar-search'); }" />
                    <button class="search-button" onclick="performSearch('sidebar-search');"><i class="fas fa-search"></i></button>
                    <div class="search-results" id="sidebar-search-results" style="display: none;"></div>
                </div>
                <div class="divider my-4 border-b border-gray-300"></div>
                <div class="flex flex-wrap gap-2 mt-6">
                    <button class="btn btn-outline"
                            onclick="navigateTo('/shop<?= isset($_GET['min']) ? '?min='.$_GET['min'].'&max='.$_GET['max'] : '' ?>', 'در حال بارگذاری همه محصولات...')"
                            <?= !isset($_GET['category']) ? 'style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05);"' : '' ?>>همه</button>
                    <?php foreach ($categories as $cat): ?>
                        <button class="btn btn-outline"
                                onclick="navigateTo('/shop?<?= isset($_GET['min']) ? 'min='.$_GET['min'].'&max='.$_GET['max'].'&' : '' ?>category=<?= htmlspecialchars($cat['category']) ?>', 'در حال فیلتر بر اساس دسته‌بندی...')"
                                <?= (isset($_GET['category']) && $_GET['category'] === $cat['category']) ? 'style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05);"' : '' ?>>
                            <?= htmlspecialchars($cat['category']) ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="col-span-3">
                <!-- فیلتر قیمت و جستجو برای موبایل -->
                <div class="md:hidden">
                    <div class="mobile-price-filter-box">
                        <div class="price-filter">
                            <div class="mobile-filter-title">فیلتر قیمت</div>
                            <div class="mobile-filter-content">
                                <div class="flex flex-col">
                                    <div class="flex items-center gap-1">
                                        <div class="price-inputs gap-1 flex-1">
                                            <div class="price-select-container flex-1">
                                                <div id="mobile-minPrice-display" class="price-select-input" data-placeholder="از ابتدا">
                                                    <?= isset($_GET['min']) && $_GET['min'] > 0 ? number_format((int)$_GET['min']) . ' ' . $toman_svg : 'از ابتدا' ?>
                                                </div>
                                                <input type="hidden" id="mobile-minPrice" value="<?= isset($_GET['min']) ? $_GET['min'] : '0' ?>">
                                            </div>
                                            <span class="px-0 text-gray-400 text-xs">تا</span>
                                            <div class="price-select-container flex-1">
                                                <div id="mobile-maxPrice-display" class="price-select-input" data-placeholder="بدون محدودیت">
                                                    <?= isset($_GET['max']) && $_GET['max'] < max($price_ranges) ? number_format((int)$_GET['max']) . ' ' . $toman_svg : 'بدون محدودیت' ?>
                                                </div>
                                                <input type="hidden" id="mobile-maxPrice" value="<?= isset($_GET['max']) ? $_GET['max'] : max($price_ranges) ?>">
                                            </div>
                                        </div>
                                        <div class="submit-price-filters flex gap-1">
                                            <button onclick="applyPriceFilter('mobile')" class="filter-btn apply">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                </svg>
                                            </button>
                                            <button onclick="clearFilters()" class="filter-btn clear">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- دکمه‌های فیلتر برای موبایل -->
                    <div class="mobile-filter-buttons md:hidden">
                        <?php
                        // URL for discounted products filter
                        $discount_url_params = $_GET;
                        $is_discounted = isset($discount_url_params['discounted']) && $discount_url_params['discounted'] == 1;
                        if ($is_discounted) {
                            unset($discount_url_params['discounted']);
                        } else {
                            $discount_url_params['discounted'] = 1;
                        }
                        $discount_url = '/shop?' . http_build_query($discount_url_params);
                        
                        // URL for available products filter
                        $available_url_params = $_GET;
                        $is_available = isset($available_url_params['available']) && $available_url_params['available'] == 1;
                        if ($is_available) {
                            unset($available_url_params['available']);
                        } else {
                            $available_url_params['available'] = 1;
                        }
                        $available_url = '/shop?' . http_build_query($available_url_params);
                        ?>
                        <a href="<?= $discount_url ?>" class="discount-filter-btn <?= $is_discounted ? 'active' : '' ?>" onclick="event.preventDefault(); navigateTo('<?= $discount_url ?>', 'در حال فیلتر محصولات تخفیف خورده...');">
                            محصولات تخفیف خورده
                        </a>
                        <a href="<?= $available_url ?>" class="discount-filter-btn <?= $is_available ? 'active' : '' ?>" onclick="event.preventDefault(); navigateTo('<?= $available_url ?>', 'در حال فیلتر کالاهای موجود...');">
                             محصولات موجود
                        </a>
                    </div>

                    <!-- جعبه جستجو بعد از فیلتر قیمت -->
                    <div class="sidebar-search-box md:hidden mt-0">
                        <input type="text" id="mobile-search" placeholder="جستجوی محصولات..."
                               value="<?= htmlspecialchars($searchQuery) ?>"
                               onkeydown="if(event.key === 'Enter') { event.preventDefault(); performSearch('mobile-search'); }" />
                        <button class="search-button" onclick="performSearch('mobile-search');"><i class="fas fa-search"></i></button>
                        <div class="search-results" id="mobile-search-results" style="display: none;"></div>
                    </div>
                </div>
                <!-- نمایش نتایج جستجو در صورت وجود -->
                <?php if (!empty($searchQuery)): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h2 class="text-xl font-bold mb-2">نتایج جستجو برای: "<?= htmlspecialchars($searchQuery) ?>"</h2>
                    <p><?= count($products) ?> محصول یافت شد</p>
                </div>
                <?php endif; ?>

                <!-- کادر مرتب‌سازی فقط برای دسکتاپ -->
                <div class="sorting-container hidden md:flex justify-between items-center bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-600 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                        </svg>
                        <span class="sorting-text" style="color: black">مرتب‌سازی:</span>
                    </div>
                    <div class="flex gap-4 items-center">
                        <!-- دکمه فیلتر تخفیف -->
                        <?php
                        $discount_url_params = $_GET;
                        $is_discounted = isset($discount_url_params['discounted']) && $discount_url_params['discounted'] == 1;
                        if ($is_discounted) {
                            unset($discount_url_params['discounted']);
                        } else {
                            $discount_url_params['discounted'] = 1;
                        }
                        $discount_url = '/shop?' . http_build_query($discount_url_params);
                        
                        // URL for available products filter
                        $available_url_params = $_GET;
                        $is_available = isset($available_url_params['available']) && $available_url_params['available'] == 1;
                        if ($is_available) {
                            unset($available_url_params['available']);
                        } else {
                            $available_url_params['available'] = 1;
                        }
                        $available_url = '/shop?' . http_build_query($available_url_params);
                        ?>
                        <a href="<?= $discount_url ?>" class="discount-filter-btn <?= $is_discounted ? 'active' : '' ?>" onclick="event.preventDefault(); navigateTo('<?= $discount_url ?>', 'در حال فیلتر محصولات تخفیف خورده...');">
                            محصولات تخفیف خورده
                        </a>
                        <a href="<?= $available_url ?>" class="discount-filter-btn <?= $is_available ? 'active' : '' ?>" onclick="event.preventDefault(); navigateTo('<?= $available_url ?>', 'در حال فیلتر کالاهای موجود...');">
                             محصولات موجود
                        </a>
                        <!-- فیلتر قیمت -->
                        <div class="price-filter inline-flex items-center gap-3 bg-white rounded-lg p-2 border border-gray-200 shadow-sm">
                            <span class="text-sm font-medium text-gray-600">قیمت:</span>
                            <div class="price-inputs">
                                <div class="custom-select-container">
                                    <select id="desktop-minPrice" class="custom-select">
                                        <option value="0" <?= (!isset($_GET['min']) || $_GET['min'] == 0) ? 'selected' : '' ?>>از ابتدا</option>
                                        <?php foreach ($price_ranges as $price): ?>
                                            <?php if ($price > 0): ?>
                                                <option value="<?= $price ?>" <?= (isset($_GET['min']) && $_GET['min'] == $price) ? 'selected' : '' ?>>
                                                    <?= number_format($price) ?> تومان
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <span class="text-gray-400">تا</span>
                                <div class="custom-select-container">
                                    <select id="desktop-maxPrice" class="custom-select">
                                        <?php foreach ($price_ranges as $price): ?>
                                            <?php if ($price > 0): ?>
                                                <option value="<?= $price ?>" <?= (isset($_GET['max']) && $_GET['max'] == $price) ? 'selected' : '' ?>>
                                                    <?= number_format($price) ?> تومان
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                        <option value="<?= max($price_ranges) ?>" <?= (!isset($_GET['max']) || $_GET['max'] >= max($price_ranges)) ? 'selected' : '' ?>>بدون محدودیت</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button onclick="applyPriceFilter('desktop')" class="filter-btn apply">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                    </svg>
                                </button>
                                <button onclick="clearFilters()" class="filter-btn clear">
                                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نمایش محصولات -->
                <?php if (empty($products)): ?>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
                        <p class="text-xl mb-2">هیچ محصولی یافت نشد!</p>
                        <p class="text-gray-600">لطفا جستجوی دیگری را امتحان کنید یا فیلترها را تغییر دهید.</p>
                    </div>
                <?php else: ?>
                    <div class="product-grid">
                        <?php foreach ($products as $product): ?>
                            <?php
                            $image_data = base64_encode($product['image_blob']);
                            $image_url = 'data:image/jpeg;base64,' . $image_data;

                            // محاسبه قیمت نهایی و درصد تخفیف با در نظر گرفتن هر دو نوع قیمت
                            $price_aed = $product['product_price_aed'] ?? null;
                            $discount_aed = $product['discount_price_aed'] ?? null;
                            $price_toman = $product['product_price'] ?? 0;
                            $discount_toman = $product['discount_price'] ?? 0;

                            $final_price = 0;
                            $old_price = 0;
                            $has_discount = false;

                            if ($price_aed !== null && $price_aed > 0) {
                                $final_price = $price_aed * $aed_to_toman_rate;
                                if ($discount_aed !== null && $discount_aed > 0) {
                                    $old_price = $final_price;
                                    $final_price = $discount_aed * $aed_to_toman_rate;
                                    $has_discount = true;
                                }
                            } elseif ($price_toman > 0) {
                                $final_price = $price_toman;
                                if ($discount_toman > 0) {
                                    $old_price = $final_price;
                                    $final_price = $discount_toman;
                                    $has_discount = true;
                                }
                            }

                            $discount_percentage = 0;
                            if ($has_discount && $old_price > 0) {
                                $discount_percentage = round((($old_price - $final_price) / $old_price) * 100);
                            }

                            $is_out_of_stock = $product['stock_quantity'] <= 0;
                            ?>
                           <div class="product-card-wrapper" data-category="<?= htmlspecialchars($product['category']) ?>">
                                <a href="/card?id=<?= $product['id'] ?>" class="product-card-link">
                                <div class="product-card" data-stock="<?= $product['stock_quantity'] ?>">
                                        <?php if ($discount_percentage > 0): ?>
                                            <span class="discount-badge">-<?= $discount_percentage ?>%</span>
                                        <?php endif; ?>

                                        <div class="product-image-container">
                                            <img class="product-image-multiply"
                                                 src="<?= $image_url ?>"
                                                 alt="<?= htmlspecialchars($product['product_name']) ?>"
                                                 loading="lazy"
                                                 onload="this.parentElement.classList.remove('skeleton')" />

                                            <?php if ($is_out_of_stock): ?>
                                                <div class="out-of-stock-overlay">
                                                    <span class="out-of-stock-text">ناموجود در انبار</span>
                                                </div>
                                            <?php endif; ?>

                                            <button class="quick-view-btn" onclick="showQuickView(<?= $product['id'] ?>)">
                                                مشاهده سریع
                                            </button>
                                        </div>

                                        <div class="product-content">
                                            <h3 class="product-name <?= ctype_alpha($product['product_name'][0]) ? 'product-name-en' : '' ?>"><?= htmlspecialchars($product['product_name']) ?></h3>
                                            <p class="product-description">
                                                <?php 
                                                $description = htmlspecialchars($product['product_description']);
                                                if (strlen($product['product_description']) > 100) {
                                                    echo substr($description, 0, 100) . '...';
                                                } else {
                                                    echo $description;
                                                }
                                                ?>
                                            </p>

                                            <?php if ($has_discount): ?>
                                                <div class="product-price">
                                                    <span class="product-old-price"><?= number_format($old_price) ?> <?= $toman_svg ?></span>
                                                    <span class="product-current-price"><?= number_format($final_price) ?> <?= $toman_svg ?></span>
                                                </div>
                                            <?php else: ?>
                                                <div class="product-price">
                                                    <span class="product-current-price"><?= number_format($final_price) ?> <?= $toman_svg ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <p class="<?= $is_out_of_stock ? 'product-out-of-stock' : 'product-stock' ?>">
                                                <?= $is_out_of_stock ? 'ناموجود در انبار' : 'موجودی: ' . $product['stock_quantity'] . ' عدد' ?>
                                            </p>

                                            <?php if ($is_out_of_stock): ?>
                                                <!-- دکمه قبلی برای محصولات ناموجود -->
                                                <button class="out-of-stock-btn" disabled>
                                                    ناموجود در انبار
                                                </button>
                                            <?php else: ?>
                                                <!-- دکمه بدون انیمیشن برای محصولات موجود -->
                                                <button class="add-to-cart" data-product-id="<?= $product['id'] ?>">
                                                    <span class="text">افزودن به سبد خرید</span><span class="IconContainer"><svg class="cart-icon" viewBox="0 0 576 512" height="1em" xmlns="http://www.w3.org/2000/svg"><path d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"></path></svg></span>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div id="quickViewModal" class="modal">
        <div class="modal-content">
            <!-- Quick view content will be loaded here -->
        </div>
    </div>

                <!-- Pagination Section -->
<?php if ($total_pages > 1): ?>
<div class="flex justify-center items-center mt-8 gap-2 pb-8 pagination-container">
    <?php
   // تنظیم پارامترهای URL
   $query_params = $_GET;
   $base_url = '?';
   if (isset($_GET['category'])) {
       $category_param = '&category=' . urlencode($_GET['category']);
   } else {
       $category_param = '';
   }
   if (isset($_GET['min'])) {
       $base_url .= 'min=' . $_GET['min'] . '&max=' . $_GET['max'] . $category_param . '&';
   } elseif (!empty($category_param)) {
       $base_url .= ltrim($category_param, '&') . '&';
   }
   ?>

   <?php if($page > 1): ?>
       <a href='<?= $base_url ?>page=<?= $page-1 ?>' class="btn btn-outline">
           <
       </a>
   <?php else: ?>
       <a href='javascript:void(0)' class="btn btn-outline" style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; cursor: not-allowed; opacity: 0.7; position: relative; overflow: hidden;">
           <span style="position: relative; z-index: 2;"><</span>
           <span style="content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;"></span>
       </a>
   <?php endif; ?>

   <?php
   // نمایش دکمه‌های صفحه‌بندی بر اساس صفحه فعلی
   if ($page == 1) {
       // نمایش دکمه صفحه 1 (فعال)
       echo "<a href='javascript:void(0)' class='btn btn-outline' style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05); position: relative; overflow: hidden;'>
            <span style='position: relative; z-index: 2;'>1</span>
            <span style='content: \"\"; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;'></span>
           </a>";

       // نمایش صفحه بعد (اگر وجود داشته باشد)
       if ($total_pages > 1) {
           echo "<a href='{$base_url}page=2' class='btn btn-outline'>2</a>";
       }

       // نمایش نقطه‌چین و صفحه آخر (اگر بیش از 2 صفحه داریم)
       if ($total_pages > 2) {
           echo "<span class='px-2'>...</span>";
           echo "<a href='{$base_url}page=$total_pages' class='btn btn-outline'>$total_pages</a>";
       }
   }
   // صفحه 2
   else if ($page == 2) {
       // نمایش صفحه قبل
       echo "<a href='{$base_url}page=1' class='btn btn-outline'>1</a>";

       // نمایش صفحه فعلی (فعال)
       echo "<a href='javascript:void(0)' class='btn btn-outline' style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05); position: relative; overflow: hidden;'>
            <span style='position: relative; z-index: 2;'>2</span>
            <span style='content: \"\"; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;'></span>
           </a>";

       // نمایش صفحه بعد (اگر وجود داشته باشد)
       if ($total_pages > 2) {
           echo "<a href='{$base_url}page=3' class='btn btn-outline'>3</a>";
       }

       // نمایش نقطه‌چین و صفحه آخر (اگر بیش از 3 صفحه داریم)
       if ($total_pages > 3) {
           echo "<span class='px-2'>...</span>";
           echo "<a href='{$base_url}page=$total_pages' class='btn btn-outline'>$total_pages</a>";
       }
   }
   // صفحه 3
   else if ($page == 3) {
       // نمایش صفحه 1
       echo "<a href='{$base_url}page=1' class='btn btn-outline'>1</a>";

       // نمایش صفحه 2
       echo "<a href='{$base_url}page=2' class='btn btn-outline'>2</a>";

       // نمایش صفحه فعلی (صفحه 3) بدون نقطه‌چین قبلی
       echo "<a href='javascript:void(0)' class='btn btn-outline' style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05); position: relative; overflow: hidden;'>
            <span style='position: relative; z-index: 2;'>3</span>
            <span style='content: \"\"; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;'></span>
           </a>";

       // نمایش صفحه بعد از صفحه فعلی (اگر وجود داشته باشد)
       if ($total_pages > 3) {
           echo "<a href='{$base_url}page=4' class='btn btn-outline'>4</a>";
           echo "<span class='px-2'>...</span>";
           echo "<a href='{$base_url}page=$total_pages' class='btn btn-outline'>$total_pages</a>";
       }
   }
   // صفحه یکی مانده به آخر
   else if ($page == $total_pages - 1) {
       // نمایش صفحه 1
       echo "<a href='{$base_url}page=1' class='btn btn-outline'>1</a>";

       // نمایش صفحه 2
       echo "<a href='{$base_url}page=2' class='btn btn-outline'>2</a>";

       // نمایش نقطه‌چین قبلی (اگر بیشتر از 3 صفحه داریم)
       if ($total_pages > 3 && $page > 3) {
           echo "<span class='px-2'>...</span>";
       }

       // نمایش صفحه قبل از صفحه فعلی
       echo "<a href='{$base_url}page=" . ($page-1) . "' class='btn btn-outline'>" . ($page-1) . "</a>";

       // نمایش صفحه فعلی (یکی مانده به آخر)
       echo "<a href='javascript:void(0)' class='btn btn-outline' style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05); position: relative; overflow: hidden;'>
            <span style='position: relative; z-index: 2;'>$page</span>
            <span style='content: \"\"; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;'></span>
           </a>";

       // نمایش صفحه بعد (صفحه آخر)
       echo "<a href='{$base_url}page=$total_pages' class='btn btn-outline'>$total_pages</a>";
   }
   // صفحات میانی (بین 3 و یکی مانده به آخر)
   else if ($page > 3 && $page < $total_pages - 1) {
       // نمایش صفحه 1
       echo "<a href='{$base_url}page=1' class='btn btn-outline'>1</a>";

       // نمایش صفحه 2
       echo "<a href='{$base_url}page=2' class='btn btn-outline'>2</a>";

       // نمایش نقطه‌چین قبلی
       echo "<span class='px-2'>...</span>";

       // نمایش صفحه قبل از صفحه فعلی
       echo "<a href='{$base_url}page=" . ($page-1) . "' class='btn btn-outline'>" . ($page-1) . "</a>";

       // نمایش صفحه فعلی
       echo "<a href='javascript:void(0)' class='btn btn-outline' style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05); position: relative; overflow: hidden;'>
            <span style='position: relative; z-index: 2;'>$page</span>
            <span style='content: \"\"; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;'></span>
           </a>";

       // نمایش صفحه بعد از صفحه فعلی
       echo "<a href='{$base_url}page=" . ($page+1) . "' class='btn btn-outline'>" . ($page+1) . "</a>";

       // نمایش نقطه‌چین بعدی
       echo "<span class='px-2'>...</span>";

       // نمایش صفحه آخر
       echo "<a href='{$base_url}page=$total_pages' class='btn btn-outline'>$total_pages</a>";
   }
   // صفحه آخر
   else if ($page == $total_pages) {
       // نمایش صفحه 1
       echo "<a href='{$base_url}page=1' class='btn btn-outline'>1</a>";

       // نمایش صفحه 2
       echo "<a href='{$base_url}page=2' class='btn btn-outline'>2</a>";

       // نمایش نقطه‌چین قبلی (اگر بیشتر از 3 صفحه داریم)
       if ($total_pages > 3) {
           echo "<span class='px-2'>...</span>";
       }

       // نمایش صفحه قبل از صفحه فعلی
       echo "<a href='{$base_url}page=" . ($page-1) . "' class='btn btn-outline'>" . ($page-1) . "</a>";

       // نمایش صفحه فعلی (صفحه آخر)
       echo "<a href='javascript:void(0)' class='btn btn-outline' style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; transform: scale(1.05); position: relative; overflow: hidden;'>
            <span style='position: relative; z-index: 2;'>$total_pages</span>
            <span style='content: \"\"; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;'></span>
           </a>";
   }
   ?>

   <?php if($page < $total_pages): ?>
       <a href='<?= $base_url ?>page=<?= $page+1 ?>' class="btn btn-outline">
           >
       </a>
   <?php else: ?>
       <a href='javascript:void(0)' class="btn btn-outline" style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important; color: white !important; border: none !important; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important; cursor: not-allowed; opacity: 0.7; position: relative; overflow: hidden;">
           <span style="position: relative; z-index: 2;">></span>
           <span style="content: ''; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.1); transform: translateX(-100%); animation: shine 2s infinite;"></span>
       </a>
   <?php endif; ?>
</div>
<?php endif; ?>
            </div>
        </div>
    </div>
<script>
             // تابع فیلتر کردن محصولات
             function filterProducts(category, element) {
    const url = new URL(window.location);

    // اضافه کردن استایل به دکمه انتخاب شده و حذف از بقیه
    const buttons = document.querySelectorAll('.btn-outline');
    buttons.forEach(btn => {
        btn.style.backgroundColor = "";
        btn.style.color = "";
        btn.style.borderColor = "";
        btn.classList.remove('btn-active');
    });

    // اعمال استایل مستقیم به دکمه انتخاب شده
    element.style.backgroundColor = "#3b82f6";
    element.style.color = "white";
    element.style.borderColor = "#3b82f6";

    // Handle 'all' category
    if (category === 'all') {
        url.searchParams.delete('category');
    } else {
        url.searchParams.set('category', category);
    }

    // Reset to first page
    url.searchParams.set('page', '1');

    // Preserve price filters
    const minPrice = document.getElementById('minPrice')?.value;
    const maxPrice = document.getElementById('maxPrice')?.value;
    if (minPrice && minPrice !== '0') url.searchParams.set('min', minPrice);
    if (maxPrice && maxPrice !== '0') url.searchParams.set('max', maxPrice);

    window.location = url.toString();
}

// اجرای کد پس از بارگذاری صفحه با تنظیم صفحه‌بندی
document.addEventListener('DOMContentLoaded', function() {
    // تنظیم استایل دکمه‌های صفحه‌بندی
    setupPaginationStyles();
});

// تابع تنظیم استایل دکمه‌های صفحه‌بندی
function setupPaginationStyles() {
    // در صورتی که استایل‌های درون خطی قبلاً اعمال نشده باشند
    const currentPage = <?= $page ?>;
    const paginationButtons = document.querySelectorAll('.pagination-container .btn-outline');

    paginationButtons.forEach(btn => {
        // اگر متن دکمه برابر با شماره صفحه جاری باشد
        if (btn.textContent.trim() == currentPage) {
            if (!btn.style.backgroundColor) {
                btn.style.backgroundColor = "#3b82f6";
                btn.style.color = "white";
                btn.style.borderColor = "#3b82f6";
            }
        }
    });
}

// تابع مدیریت کلیک روی دکمه‌های صفحه‌بندی
function handlePaginationClick(element, event) {
    if (element.style.backgroundColor === "#3b82f6" || element.textContent.trim() === '...') {
        event.preventDefault();
        return;
    }

    // اضافه کردن استایل به دکمه انتخاب شده و حذف از بقیه
    const paginationButtons = document.querySelectorAll('.pagination-container .btn-outline');
    paginationButtons.forEach(btn => {
        btn.style.backgroundColor = "";
        btn.style.color = "";
        btn.style.borderColor = "";
        btn.classList.remove('btn-active');
    });

    // اعمال استایل مستقیم به دکمه انتخاب شده
    element.style.backgroundColor = "#3b82f6";
    element.style.color = "white";
    element.style.borderColor = "#3b82f6";
}

// اضافه کردن انیمیشن به کارت‌های نمایش داده شده
const visibleCards = document.querySelectorAll('.product-card-wrapper[style="display: block"]');
visibleCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    setTimeout(() => {
        card.style.transition = 'all 0.3s ease-in-out';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, index * 100);
});

// اضافه کردن event listener برای جستجو
document.addEventListener('DOMContentLoaded', function() {
    // برای سرچ باکس سایدبار دسکتاپ
    const sidebarSearchInput = document.querySelector('#sidebar-search');
    const sidebarSearchResults = document.querySelector('#sidebar-search-results');

    // برای سرچ باکس موبایل
    const mobileSearchInput = document.querySelector('#mobile-search');
    const mobileSearchResults = document.querySelector('#mobile-search-results');

    // تابع جستجو
    function handleSearch(searchInput, resultsContainer) {
        if (!searchInput) return;

        // اضافه کردن قابلیت جستجو با Enter
        searchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const query = searchInput.value.trim();
                if (query !== '') {
                    // هدایت به صفحه جستجو با پارامتر q
                    window.location.href = `/shop?q=${encodeURIComponent(query)}`;
                }
            }
        });

        // اضافه کردن دکمه جستجو برای موبایل
        const searchButton = document.createElement('button');
        searchButton.className = 'search-button';
        searchButton.innerHTML = '<i class="fas fa-search"></i>';
        searchButton.addEventListener('click', function() {
            const query = searchInput.value.trim();
            if (query !== '') {
                window.location.href = `/shop?q=${encodeURIComponent(query)}`;
            }
        });

        // اضافه کردن دکمه به کنار فیلد جستجو
        const parentElement = searchInput.parentElement;
        if (parentElement && !parentElement.querySelector('.search-button')) {
            parentElement.appendChild(searchButton);
        }
    }

    // اعمال جستجو برای هر دو حالت
    handleSearch(sidebarSearchInput, sidebarSearchResults);
    handleSearch(mobileSearchInput, mobileSearchResults);
});

// ایجاد المان پیام برای زمانی که نتیجه‌ای پیدا نشود
function createNoResultsMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.id = 'no-results-message';
    messageDiv.className = 'text-center p-4 mt-4 text-red-500 font-medium';
    messageDiv.textContent = 'محصولی با این مشخصات یافت نشد';
    messageDiv.style.display = 'none';

    // افزودن به DOM بعد از آخرین کارت محصول یا در انتهای container
    const productsContainer = document.querySelector('.col-span-3') || document.body;
    productsContainer.appendChild(messageDiv);

    return messageDiv;
}
</script>
 <script>
// تابع نمایش انیمیشن بارگذاری
function showLoadingAnimation(message) {
    // جلوگیری از نمایش چند انیمیشن همزمان
    if (document.querySelector('.loading-animation')) {
        return;
    }
    const loadingAnimation = document.createElement('div');
    loadingAnimation.className = 'loading-animation';
    loadingAnimation.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">${message}</div>
    `;
    document.body.appendChild(loadingAnimation);
}

// تابع برای هدایت به صفحه جدید با نمایش انیمیشن
function navigateTo(url, message) {
    showLoadingAnimation(message);
    setTimeout(() => {
        window.location.href = url;
    }, 300);
}

// تابع برای انجام جستجو
function performSearch(inputId) {
    const query = document.getElementById(inputId).value.trim();
    if (query !== '') {
        const url = `/shop?q=${encodeURIComponent(query)}`;
        navigateTo(url, 'در حال جستجوی محصولات...');
    }
}

// کلاس برای مدیریت انتخاب قیمت با مدال
class PriceSelect {
    constructor(inputDisplay, inputHidden, options, prefix, isMax = false) {
        this.inputDisplay = inputDisplay;
        this.inputHidden = inputHidden;
        this.options = options;
        this.prefix = prefix;
        this.isMax = isMax;
        this.isOpen = false;

        // ایجاد مدال برای نمایش گزینه‌ها
        this.createModal();
        this.setupEventListeners();
    }

    // ایجاد مدال برای نمایش گزینه‌ها
    createModal() {
        // بررسی وجود مدال قبلی
        let existingModal = document.getElementById('price-select-modal');
        if (!existingModal) {
            // ایجاد مدال جدید
            this.modal = document.createElement('div');
            this.modal.id = 'price-select-modal';
            this.modal.className = 'price-select-modal';
            this.modal.style.display = 'none';

            // ایجاد پس‌زمینه تیره
            this.overlay = document.createElement('div');
            this.overlay.className = 'price-modal-overlay';

            // ایجاد محتوای مدال
            this.modalContent = document.createElement('div');
            this.modalContent.className = 'price-modal-content';

            // ایجاد عنوان مدال
            this.modalTitle = document.createElement('div');
            this.modalTitle.className = 'price-modal-title';
            this.modalTitle.textContent = this.isMax ? 'انتخاب حداکثر قیمت' : 'انتخاب حداقل قیمت';

            // ایجاد دکمه بستن
            this.closeButton = document.createElement('button');
            this.closeButton.className = 'price-modal-close';
            this.closeButton.innerHTML = '&times;';
            this.closeButton.addEventListener('click', () => this.hideModal());

            // ایجاد لیست گزینه‌ها
            this.optionsList = document.createElement('div');
            this.optionsList.className = 'price-modal-options';

            // افزودن اجزا به مدال
            this.modalContent.appendChild(this.closeButton);
            this.modalContent.appendChild(this.modalTitle);
            this.modalContent.appendChild(this.optionsList);
            this.modal.appendChild(this.overlay);
            this.modal.appendChild(this.modalContent);

            // افزودن مدال به بدنه صفحه
            document.body.appendChild(this.modal);
        } else {
            // استفاده از مدال موجود
            this.modal = existingModal;
            this.overlay = this.modal.querySelector('.price-modal-overlay');
            this.modalContent = this.modal.querySelector('.price-modal-content');
            this.optionsList = this.modal.querySelector('.price-modal-options');
        }
    }

    setupEventListeners() {
        // کلیک روی فیلد نمایش
        this.inputDisplay.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showModal();
        });

        // کلیک روی پس‌زمینه تیره برای بستن مدال
        this.overlay.addEventListener('click', () => {
            this.hideModal();
        });

        // جلوگیری از بسته شدن مدال با کلیک روی محتوای آن
        this.modalContent.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    // نمایش مدال و رندر گزینه‌ها
    showModal() {
        // رندر گزینه‌ها
        this.renderOptions();

        // نمایش مدال
        this.modal.style.display = 'block';
        this.isOpen = true;

        // غیرفعال کردن اسکرول صفحه
        document.body.classList.add('modal-open');

        // تنظیم عنوان مدال
        const titleElement = this.modal.querySelector('.price-modal-title');
        if (titleElement) {
            titleElement.textContent = this.isMax ? 'انتخاب حداکثر قیمت' : 'انتخاب حداقل قیمت';
        }
    }

    // مخفی کردن مدال
    hideModal() {
        this.modal.style.display = 'none';
        this.isOpen = false;

        // فعال کردن مجدد اسکرول صفحه
        document.body.classList.remove('modal-open');
    }

    // رندر گزینه‌ها در مدال
    renderOptions() {
        this.optionsList.innerHTML = '';

        this.options.forEach(price => {
            const option = document.createElement('div');
            option.className = 'price-modal-option';

            if (price === 0 && !this.isMax) {
                option.textContent = 'از ابتدا';
            } else if (price === Math.max(...this.options) && this.isMax) {
                option.textContent = 'بدون محدودیت';
            } else {
                option.innerHTML = this.formatPrice(price) + ' ' + `<?= $toman_svg ?>`;
            }

            if (parseInt(this.inputHidden.value) === price) {
                option.classList.add('selected');
            }

            option.addEventListener('click', () => {
                this.selectPrice(price, option.textContent);
                this.hideModal();
            });

            this.optionsList.appendChild(option);
        });
    }

    // انتخاب قیمت
    selectPrice(price, displayText) {
        this.inputHidden.value = price;
        
        // تنظیم متن نمایشی با آیکون تومان
        if (price === 0 && !this.isMax) {
            this.inputDisplay.innerHTML = 'از ابتدا';
        } else if (price === Math.max(...this.options) && this.isMax) {
            this.inputDisplay.innerHTML = 'بدون محدودیت';
        } else {
            this.inputDisplay.innerHTML = this.formatPrice(price) + ' ' + `<?= $toman_svg ?>`;
        }

        // اگر حداقل قیمت بیشتر از حداکثر شد، حداکثر را تنظیم کن
        if (!this.isMax) {
            const maxInput = document.getElementById(`${this.prefix}-maxPrice`);
            const maxDisplay = document.getElementById(`${this.prefix}-maxPrice-display`);
            if (parseInt(maxInput.value) < price) {
                // پیدا کردن اولین گزینه بزرگتر یا مساوی در آرایه گزینه‌ها
                const newMaxVal = this.options.find(val => val >= price);
                if (newMaxVal) {
                    maxInput.value = newMaxVal;
                    let newDisplayText;
                    if (newMaxVal === Math.max(...this.options)) {
                        newDisplayText = 'بدون محدودیت';
                    } else {
                        newDisplayText = this.formatPrice(newMaxVal) + ' ' + `<?= $toman_svg ?>`;
                    }
                    maxDisplay.innerHTML = newDisplayText;
                }
            }
        }

        // اگر حداکثر قیمت کمتر از حداقل شد، حداقل را تنظیم کن
        if (this.isMax) {
            const minInput = document.getElementById(`${this.prefix}-minPrice`);
            const minDisplay = document.getElementById(`${this.prefix}-minPrice-display`);
            if (parseInt(minInput.value) > price && price !== Math.max(...this.options)) {
                // پیدا کردن آخرین گزینه کوچکتر یا مساوی در آرایه گزینه‌ها
                const newMinVal = this.options.filter(val => val <= price).pop();
                if (newMinVal !== undefined) {
                    minInput.value = newMinVal;
                    let newDisplayText;
                    if (newMinVal === 0) {
                        newDisplayText = 'از ابتدا';
                    } else {
                        newDisplayText = this.formatPrice(newMinVal) + ' ' + `<?= $toman_svg ?>`;
                    }
                    minDisplay.innerHTML = newDisplayText;
                }
            }
        }

        // ارسال رویداد تغییر
        this.inputHidden.dispatchEvent(new Event('change'));
    }

    formatPrice(price) {
        return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
}

// راه‌اندازی مدال‌های انتخاب قیمت
document.addEventListener('DOMContentLoaded', function() {
    const priceRanges = [<?= implode(', ', $price_ranges) ?>];
    
    // تعریف متغیر آیکون تومان برای JavaScript
    const tomanSvgJs = `<?= $toman_svg ?>`;

    // متغیرهای جهانی برای دسترسی به مدال‌های قیمت
    let priceSelects = [];

    // فقط مدال‌های موبایل
    priceSelects.push(new PriceSelect(
        document.getElementById('mobile-minPrice-display'),
        document.getElementById('mobile-minPrice'),
        priceRanges,
        'mobile'
    ));

    priceSelects.push(new PriceSelect(
        document.getElementById('mobile-maxPrice-display'),
        document.getElementById('mobile-maxPrice'),
        priceRanges,
        'mobile',
        true
    ));

    // اضافه کردن رویداد کلید Escape برای بستن مدال
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            priceSelects.forEach(select => {
                if (select.isOpen) {
                    select.hideModal();
                }
            });
        }
    });

    // اضافه کردن کلاس has-value به select‌های دسکتاپ با مقادیر انتخاب شده
    const desktopMinPrice = document.getElementById('desktop-minPrice');
    const desktopMaxPrice = document.getElementById('desktop-maxPrice');

    // بررسی و اعمال کلاس برای حداقل قیمت
    if (desktopMinPrice && desktopMinPrice.value !== '0') {
        desktopMinPrice.classList.add('has-value');
    }

    // بررسی و اعمال کلاس برای حداکثر قیمت
    if (desktopMaxPrice && desktopMaxPrice.value !== String(Math.max(...priceRanges))) {
        desktopMaxPrice.classList.add('has-value');
    }

    // اضافه کردن رویداد تغییر برای اعمال کلاس به صورت پویا
    desktopMinPrice.addEventListener('change', function() {
        if (this.value !== '0') {
            this.classList.add('has-value');
        } else {
            this.classList.remove('has-value');
        }
    });

    desktopMaxPrice.addEventListener('change', function() {
        if (this.value !== String(Math.max(...priceRanges))) {
            this.classList.add('has-value');
        } else {
            this.classList.remove('has-value');
        }
    });
});

// اعمال فیلتر قیمت
function applyPriceFilter(prefix) {
    const minPrice = document.getElementById(`${prefix}-minPrice`).value;
    const maxPrice = document.getElementById(`${prefix}-maxPrice`).value;

    const url = new URL(window.location.href);

    if (minPrice > 0) {
        url.searchParams.set('min', minPrice);
    } else {
        url.searchParams.delete('min');
    }

    if (maxPrice < <?= max($price_ranges) ?>) {
        url.searchParams.set('max', maxPrice);
    } else {
        url.searchParams.delete('max');
    }

    url.searchParams.set('page', '1');

    // نمایش انیمیشن بارگذاری
    navigateTo(url.toString(), 'در حال اعمال فیلتر قیمت...');
}
</script>

<!-- Scroll to Top Button -->
<div class="scroll-to-top">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
        class="w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M5 15l7-7 7 7" />
    </svg>
    <span class="mt-1 text-sm"></span>
</div>

<script>
    // کد مربوط به دکمه بازگشت به بالای صفحه
    document.addEventListener('DOMContentLoaded', function() {
        const scrollToTopButton = document.querySelector('.scroll-to-top');

        // نمایش یا مخفی کردن دکمه بر اساس موقعیت اسکرول
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) { // نمایش دکمه بعد از 300 پیکسل اسکرول
                scrollToTopButton.classList.add('show');
            } else {
                scrollToTopButton.classList.remove('show');
            }
        });

        // اسکرول به بالای صفحه با کلیک روی دکمه
        scrollToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    });
</script>

<script>
// پاک کردن همه فیلترها
function clearFilters() {
    const url = new URL(window.location.href);
    url.searchParams.delete('min');
    url.searchParams.delete('max');
    url.searchParams.delete('category');
    url.searchParams.delete('q');
    window.location.href = url.toString();
}

document.addEventListener('DOMContentLoaded', function() {
    const addToCartButtons = document.querySelectorAll('.add-to-cart');

    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.getAttribute('data-product-id');

            fetch('add_to_cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `product_id=${productId}&quantity=1`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'موفقیت',
                            text: data.message,
                            confirmButtonText: 'باشه',
                            preConfirm: () => {
                                window.location.href = data.redirect;
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطا',
                            text: data.message,
                            confirmButtonText: 'باشه'
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطا',
                        text: 'خطای غیرمنتظره‌ای رخ داد',
                        confirmButtonText: 'باشه'
                    });
                });
        });
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const productCards = document.querySelectorAll('.product-card-wrapper');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.remove('skeleton');
                observer.unobserve(entry.target);
            }
        });
    });

    productCards.forEach(card => observer.observe(card));
});

function showQuickView(productId) {
    // Implement quick view functionality
    event.preventDefault();
    // Add AJAX call to load product details
    // Show modal with product details
}

const handleStockDisplay = () => {
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        const stockQuantity = parseInt(card.dataset.stock);
        if (stockQuantity <= 0) {
            card.classList.add('out-of-stock');
        }
    });
};

document.addEventListener('DOMContentLoaded', handleStockDisplay);

document.addEventListener('DOMContentLoaded', function() {
    // دریافت مقدار دسته‌بندی فعلی از URL
    const urlParams = new URLSearchParams(window.location.search);
    const currentCategory = urlParams.get('category');

    // یافتن و فعال کردن دکمه دسته‌بندی مناسب
    const categoryButtons = document.querySelectorAll('.btn-outline');
    categoryButtons.forEach(button => {
        // پاک کردن کلاس فعال از همه دکمه‌ها
        button.classList.remove('btn-active');

        // مشخص کردن دکمه فعال
        if (!currentCategory && button.textContent.trim() === 'همه') {
            button.classList.add('btn-active');
        } else if (currentCategory && button.textContent.trim() === currentCategory) {
            button.classList.add('btn-active');
        }
    });
});

// فیلتر کردن محصولات بر اساس دسته‌بندی
function filterProducts(category, element) {
    const url = new URL(window.location);

    // اضافه کردن کلاس active به دکمه انتخاب شده
    const buttons = document.querySelectorAll('.btn-outline');
    buttons.forEach(btn => btn.classList.remove('btn-active'));
    element.classList.add('btn-active');

    // Handle 'all' category
    if (category === 'all') {
        url.searchParams.delete('category');
    } else {
        url.searchParams.set('category', category);
    }

    // Reset to first page
    url.searchParams.set('page', '1');

    // Preserve price filters
    const minPrice = document.getElementById('minPrice')?.value;
    const maxPrice = document.getElementById('maxPrice')?.value;
    if (minPrice && minPrice !== '0') url.searchParams.set('min', minPrice);
    if (maxPrice && maxPrice !== '0') url.searchParams.set('max', maxPrice);

    window.location = url;
}
</script>

<!-- اسکریپت جستجوی پیشنهادی غیرفعال شده است
<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarSearch = document.getElementById('sidebar-search');
    const searchResults = document.getElementById('sidebar-search-results');

    let debounceTimer;

    // جستجوی پویا با تایپ کاربر
    sidebarSearch.addEventListener('input', function() {
        const query = sidebarSearch.value.trim();

        // پاک کردن تایمر قبلی برای جلوگیری از درخواست‌های متعدد
        clearTimeout(debounceTimer);

        if (query === '') {
            searchResults.style.display = 'none';
            return;
        }

        // تأخیر در ارسال درخواست جستجو (300 میلی‌ثانیه)
        debounceTimer = setTimeout(() => {
            searchProducts(query);
        }, 300);
    });

    // ارسال فرم با فشردن کلید Enter
    sidebarSearch.addEventListener('keydown', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            const query = sidebarSearch.value.trim();
            if (query !== '') {
                window.location.href = `shop.php?q=${encodeURIComponent(query)}`;
            }
        }
    });

    // مخفی کردن نتایج جستجو هنگام کلیک خارج از باکس جستجو
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.sidebar-search-box')) {
            searchResults.style.display = 'none';
        }
    });

    // جستجوی محصولات با API
    function searchProducts(query) {
        fetch(`search_api.php?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data, query);
            })
            .catch(error => {
                console.error('خطا در دریافت نتایج جستجو:', error);
            });
    }

    // نمایش نتایج جستجو
    function displaySearchResults(data, query) {
        searchResults.innerHTML = '';
        searchResults.style.display = 'block';

        const products = data.results;
        const categories = data.categories;

        if (products.length === 0 && categories.length === 0) {
            searchResults.innerHTML = `
                <div class="search-no-results">
                    محصولی با عبارت "<span class="font-bold">${query}</span>" یافت نشد
                </div>
            `;
            return;
        }

        // نمایش دسته‌بندی‌ها
        if (categories.length > 0) {
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'p-2 bg-gray-100 font-bold text-sm';
            categoryHeader.textContent = 'دسته‌بندی‌ها';
            searchResults.appendChild(categoryHeader);

            categories.forEach(category => {
                const categoryItem = document.createElement('div');
                categoryItem.className = 'search-result-item';
                categoryItem.innerHTML = `
                    <div class="search-result-info">
                        <div class="search-result-title">${category}</div>
                    </div>
                `;

                categoryItem.addEventListener('click', () => {
                    window.location.href = `shop.php?category=${encodeURIComponent(category)}`;
                });

                searchResults.appendChild(categoryItem);
            });
        }

        // نمایش محصولات
        if (products.length > 0) {
            const productHeader = document.createElement('div');
            productHeader.className = 'p-2 bg-gray-100 font-bold text-sm';
            productHeader.textContent = 'محصولات';
            searchResults.appendChild(productHeader);

            products.forEach(product => {
                const productItem = document.createElement('div');
                productItem.className = 'search-result-item';

                let imageHtml = '';
                if (product.image) {
                    imageHtml = `<img src="data:image/jpeg;base64,${product.image}" class="search-result-image" alt="${product.name}">`;
                } else {
                    imageHtml = `<div class="search-result-image bg-gray-200 flex items-center justify-center">تصویر ندارد</div>`;
                }

                productItem.innerHTML = `
                    ${imageHtml}
                    <div class="search-result-info">
                        <div class="search-result-title">${product.name}</div>
                        <div class="search-result-category">${product.category}</div>
                    </div>
                    <div class="search-result-price">${numberWithCommas(product.price)} <span class="toman-svg-js">$toman_svg_js</span></div>
                `;

                productItem.addEventListener('click', () => {
                    window.location.href = `card.php?id=${product.id}`;
                });

                searchResults.appendChild(productItem);
            });
        }
    }

    // تبدیل اعداد به فرمت هزارگان
    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
});
</script>
-->
</body>

</html>
<br>
<?php
include("footer.php");
?>
